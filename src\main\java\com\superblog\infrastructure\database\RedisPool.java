package com.superblog.infrastructure.database;

import com.superblog.config.RedisConfig;
import io.vertx.core.Vertx;
import io.vertx.redis.client.Redis;
import io.vertx.redis.client.RedisOptions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Redis连接池工厂类
 * 负责创建和配置Redis客户端
 */
public class RedisPool {

    private static final Logger logger = LoggerFactory.getLogger(RedisPool.class);

    /**
     * 创建Redis客户端
     */
    public static Redis create(Vertx vertx, RedisConfig config) {
        logger.info("正在创建Redis客户端...");

        RedisOptions options = new RedisOptions()
            .setConnectionString(config.getConnectionString())
            .setMaxPoolSize(config.maxPoolSize())
            .setMaxWaitingHandlers(config.maxWaitingHandlers());

        Redis redis = Redis.createClient(vertx, options);
        
        logger.info("Redis客户端创建完成，连接地址: {}:{}", config.host(), config.port());
        
        return redis;
    }
}
