package com.superblog;

import io.vertx.core.Vertx;
import io.vertx.core.http.HttpServer;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.handler.BodyHandler;
import io.vertx.ext.web.handler.CorsHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 简化的SuperBlog启动类
 * 用于快速启动和测试基本功能
 */
public class SimpleApplication {

    private static final Logger logger = LoggerFactory.getLogger(SimpleApplication.class);

    public static void main(String[] args) {
        // 设置日志
        System.setProperty("vertx.logger-delegate-factory-class-name", 
            "io.vertx.core.logging.SLF4JLogDelegateFactory");

        logger.info("🚀 启动简化版 SuperBlog...");

        // 创建Vertx实例
        Vertx vertx = Vertx.vertx();

        // 创建HTTP服务器
        HttpServer server = vertx.createHttpServer();

        // 创建路由
        Router router = Router.router(vertx);

        // 配置CORS
        CorsHandler corsHandler = CorsHandler.create()
            .addOrigin("*")
            .allowedMethod(io.vertx.core.http.HttpMethod.GET)
            .allowedMethod(io.vertx.core.http.HttpMethod.POST)
            .allowedMethod(io.vertx.core.http.HttpMethod.PUT)
            .allowedMethod(io.vertx.core.http.HttpMethod.DELETE)
            .allowedMethod(io.vertx.core.http.HttpMethod.OPTIONS)
            .allowedHeader("Content-Type")
            .allowedHeader("Authorization");

        router.route().handler(corsHandler);
        router.route().handler(BodyHandler.create());

        // 健康检查接口
        router.get("/api/health").handler(context -> {
            JsonObject response = new JsonObject()
                .put("status", "UP")
                .put("timestamp", System.currentTimeMillis())
                .put("service", "SuperBlog Backend")
                .put("version", "1.0.0");

            context.response()
                .putHeader("Content-Type", "application/json")
                .end(response.encode());
        });

        // AI图片生成接口（模拟）
        router.post("/api/ai/text-to-image").handler(context -> {
            JsonObject requestBody = context.body().asJsonObject();
            String prompt = requestBody.getString("prompt", "默认提示词");

            JsonObject response = new JsonObject()
                .put("success", true)
                .put("code", 200)
                .put("message", "文生图任务创建成功")
                .put("data", new JsonObject()
                    .put("id", System.currentTimeMillis())
                    .put("prompt", prompt)
                    .put("status", "PENDING")
                    .put("created_at", System.currentTimeMillis()))
                .put("timestamp", System.currentTimeMillis());

            context.response()
                .putHeader("Content-Type", "application/json")
                .end(response.encode());
        });

        // 获取公开图片生成记录（模拟）
        router.get("/api/ai/generations/public").handler(context -> {
            JsonObject response = new JsonObject()
                .put("success", true)
                .put("code", 200)
                .put("message", "获取成功")
                .put("data", new io.vertx.core.json.JsonArray()
                    .add(new JsonObject()
                        .put("id", 1)
                        .put("prompt", "一只可爱的橘猫")
                        .put("status", "COMPLETED")
                        .put("generated_image_url", "https://example.com/cat.jpg"))
                    .add(new JsonObject()
                        .put("id", 2)
                        .put("prompt", "美丽的风景")
                        .put("status", "COMPLETED")
                        .put("generated_image_url", "https://example.com/landscape.jpg")))
                .put("timestamp", System.currentTimeMillis());

            context.response()
                .putHeader("Content-Type", "application/json")
                .end(response.encode());
        });

        // 通义千问AI接口
        router.post("/api/ai/chat").handler(context -> {
            JsonObject requestBody = context.body().asJsonObject();
            String question = requestBody.getString("question", "你好");

            // 模拟通义千问响应
            String answer = "这是通义千问的模拟回答：" + question + "。实际使用时会调用真实的API。";

            JsonObject response = new JsonObject()
                .put("success", true)
                .put("code", 200)
                .put("message", "问答成功")
                .put("data", new JsonObject()
                    .put("question", question)
                    .put("answer", answer)
                    .put("timestamp", System.currentTimeMillis()));

            context.response()
                .putHeader("Content-Type", "application/json")
                .end(response.encode());
        });

        router.post("/api/ai/generate-article").handler(context -> {
            JsonObject requestBody = context.body().asJsonObject();
            String topic = requestBody.getString("topic", "技术文章");

            String article = "# " + topic + "\n\n这是由通义千问生成的技术文章示例。\n\n## 简介\n\n" +
                "本文将介绍" + topic + "的相关内容...\n\n## 详细内容\n\n待实际API集成后生成完整内容。";

            JsonObject response = new JsonObject()
                .put("success", true)
                .put("code", 200)
                .put("message", "文章生成成功")
                .put("data", new JsonObject()
                    .put("topic", topic)
                    .put("article", article)
                    .put("wordCount", article.length()));

            context.response()
                .putHeader("Content-Type", "application/json")
                .end(response.encode());
        });

        router.post("/api/ai/optimize-prompt").handler(context -> {
            JsonObject requestBody = context.body().asJsonObject();
            String input = requestBody.getString("input", "一只猫");

            String optimizedPrompt = "A beautiful cat sitting gracefully, detailed fur texture, " +
                "soft lighting, high quality, photorealistic, 4k resolution";

            JsonObject response = new JsonObject()
                .put("success", true)
                .put("code", 200)
                .put("message", "提示词优化成功")
                .put("data", new JsonObject()
                    .put("originalInput", input)
                    .put("optimizedPrompt", optimizedPrompt));

            context.response()
                .putHeader("Content-Type", "application/json")
                .end(response.encode());
        });

        router.get("/api/ai/qwen/test").handler(context -> {
            JsonObject response = new JsonObject()
                .put("success", true)
                .put("code", 200)
                .put("message", "通义千问API连接正常（模拟）")
                .put("data", new JsonObject()
                    .put("connected", true)
                    .put("apiKey", "sk-4606dfde828a4f9aa7a43f5d53dddb9e")
                    .put("model", "qwen-turbo"));

            context.response()
                .putHeader("Content-Type", "application/json")
                .end(response.encode());
        });

        // 其他模块的占位符接口
        router.get("/api/videos").handler(context -> {
            JsonObject response = new JsonObject()
                .put("success", true)
                .put("message", "视频功能开发中")
                .put("data", new io.vertx.core.json.JsonArray());
            context.response()
                .putHeader("Content-Type", "application/json")
                .end(response.encode());
        });

        router.get("/api/articles").handler(context -> {
            JsonObject response = new JsonObject()
                .put("success", true)
                .put("message", "文章功能开发中")
                .put("data", new io.vertx.core.json.JsonArray());
            context.response()
                .putHeader("Content-Type", "application/json")
                .end(response.encode());
        });

        router.get("/api/projects").handler(context -> {
            JsonObject response = new JsonObject()
                .put("success", true)
                .put("message", "项目功能开发中")
                .put("data", new io.vertx.core.json.JsonArray());
            context.response()
                .putHeader("Content-Type", "application/json")
                .end(response.encode());
        });

        // 404处理
        router.route().last().handler(context -> {
            JsonObject response = new JsonObject()
                .put("success", false)
                .put("code", 404)
                .put("message", "API接口不存在")
                .put("timestamp", System.currentTimeMillis());

            context.response()
                .setStatusCode(404)
                .putHeader("Content-Type", "application/json")
                .end(response.encode());
        });

        // 启动服务器
        int port = 8080;
        server.requestHandler(router)
            .listen(port)
            .onSuccess(httpServer -> {
                logger.info("✅ SuperBlog 启动成功！");
                logger.info("🌐 访问地址: http://localhost:{}", port);
                logger.info("❤️ 健康检查: http://localhost:{}/api/health", port);
                logger.info("🤖 AI接口测试: POST http://localhost:{}/api/ai/text-to-image", port);
            })
            .onFailure(throwable -> {
                logger.error("❌ SuperBlog 启动失败", throwable);
                System.exit(1);
            });
    }
}
