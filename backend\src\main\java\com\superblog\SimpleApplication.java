package com.superblog;

import com.superblog.infrastructure.web.SimpleRouterFactory;
import io.vertx.core.Vertx;
import io.vertx.core.http.HttpServer;
import io.vertx.ext.web.Router;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 简化的SuperBlog启动类
 * 用于快速启动和测试基本功能
 */
public class SimpleApplication {

    private static final Logger logger = LoggerFactory.getLogger(SimpleApplication.class);

    public static void main(String[] args) {
        // 设置日志
        System.setProperty("vertx.logger-delegate-factory-class-name", 
            "io.vertx.core.logging.SLF4JLogDelegateFactory");

        logger.info("🚀 启动简化版 SuperBlog...");

        // 创建Vertx实例
        Vertx vertx = Vertx.vertx();

        // 创建HTTP服务器
        HttpServer server = vertx.createHttpServer();

        // 使用SimpleRouterFactory创建路由
        SimpleRouterFactory routerFactory = new SimpleRouterFactory(vertx);
        Router router = routerFactory.createRouter();

        // 启动服务器
        int port = 8080;
        server.requestHandler(router)
            .listen(port)
            .onSuccess(httpServer -> {
                logger.info("✅ SuperBlog 启动成功！");
                logger.info("🌐 访问地址: http://localhost:{}", port);
                logger.info("❤️ 健康检查: http://localhost:{}/api/health", port);
                logger.info("🤖 AI接口测试: POST http://localhost:{}/api/ai/text-to-image", port);
            })
            .onFailure(throwable -> {
                logger.error("❌ SuperBlog 启动失败", throwable);
                System.exit(1);
            });
    }
}
