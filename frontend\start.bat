@echo off
chcp 65001 >nul

echo 🚀 启动 SuperBlog Frontend...
echo ================================

REM 检查Node.js版本
echo 📋 检查环境...
for /f "tokens=*" %%i in ('node -v') do set node_version=%%i
for /f "tokens=*" %%i in ('npm -v') do set npm_version=%%i
echo Node.js版本: %node_version%
echo npm版本: %npm_version%

REM 检查依赖是否已安装
if not exist "node_modules" (
    echo 📦 安装依赖...
    npm install
)

REM 启动开发服务器
echo 🌐 启动开发服务器...
echo 访问地址: http://localhost:3000
echo 如果3000端口被占用，Vite会自动选择其他端口
echo ================================

npm run dev

pause
