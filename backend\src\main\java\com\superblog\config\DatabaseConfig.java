package com.superblog.config;

/**
 * 数据库配置类
 */
public record DatabaseConfig(
    String host,
    Integer port,
    String database,
    String username,
    String password,
    Integer maxPoolSize,
    Integer connectTimeout,
    Integer idleTimeout
) {
    
    /**
     * 获取JDBC URL
     */
    public String getJdbcUrl() {
        return String.format("jdbc:postgresql://%s:%d/%s", host, port, database);
    }
    
    /**
     * 获取Vert.x PostgreSQL连接字符串
     */
    public String getVertxConnectionString() {
        return String.format("postgresql://%s:%s@%s:%d/%s", 
            username, password, host, port, database);
    }
}
