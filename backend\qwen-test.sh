#!/bin/bash

# 通义千问API测试脚本

echo "=== 通义千问API测试 ==="

BASE_URL="http://localhost:8080/api"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试函数
test_api() {
    local name="$1"
    local method="$2"
    local url="$3"
    local data="$4"
    
    echo -e "\n${YELLOW}测试: $name${NC}"
    echo "请求: $method $url"
    
    if [ -n "$data" ]; then
        echo "数据: $data"
        response=$(curl -s -w "\n%{http_code}" -X "$method" \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$url")
    else
        response=$(curl -s -w "\n%{http_code}" -X "$method" "$url")
    fi
    
    # 分离响应体和状态码
    body=$(echo "$response" | head -n -1)
    status=$(echo "$response" | tail -n 1)
    
    echo "状态码: $status"
    echo "响应: $body"
    
    if [ "$status" = "200" ]; then
        echo -e "${GREEN}✓ 测试通过${NC}"
    else
        echo -e "${RED}✗ 测试失败${NC}"
    fi
}

# 等待服务启动
echo -e "${YELLOW}等待服务启动...${NC}"
for i in {1..30}; do
    if curl -s "$BASE_URL/health" > /dev/null 2>&1; then
        echo -e "${GREEN}服务已启动${NC}"
        break
    fi
    echo -n "."
    sleep 1
done

echo ""

# 1. 测试API连接
test_api "通义千问API连接测试" "GET" "$BASE_URL/ai/qwen/test" ""

# 2. 智能问答
test_api "智能问答" "POST" "$BASE_URL/ai/chat" '{
    "question": "什么是Java？",
    "context": "编程语言学习"
}'

# 3. 生成博客文章
test_api "生成博客文章" "POST" "$BASE_URL/ai/generate-article" '{
    "topic": "Java Spring Boot入门教程",
    "outline": "1. 简介\n2. 环境搭建\n3. 第一个应用\n4. 总结"
}'

# 4. 优化绘画提示词
test_api "优化绘画提示词" "POST" "$BASE_URL/ai/optimize-prompt" '{
    "input": "一只可爱的橘猫坐在樱花树下"
}'

# 5. 自由文本生成
test_api "自由文本生成" "POST" "$BASE_URL/ai/generate-text" '{
    "prompt": "请写一段关于人工智能发展的文字",
    "systemMessage": "你是一个技术专家",
    "maxTokens": 500,
    "temperature": 0.7
}'

# 6. 复杂问答测试
test_api "复杂技术问答" "POST" "$BASE_URL/ai/chat" '{
    "question": "如何在Spring Boot中集成Redis？",
    "context": "我正在开发一个Web应用，需要使用缓存"
}'

# 7. 创意写作测试
test_api "创意写作" "POST" "$BASE_URL/ai/generate-article" '{
    "topic": "未来的编程语言",
    "outline": "1. 当前编程语言的局限性\n2. 未来编程语言的特征\n3. AI辅助编程\n4. 展望"
}'

echo -e "\n${GREEN}=== 通义千问API测试完成 ===${NC}"

# 显示使用说明
echo -e "\n${YELLOW}=== 使用说明 ===${NC}"
echo "1. 智能问答: POST /api/ai/chat"
echo "   - question: 问题内容"
echo "   - context: 上下文信息（可选）"
echo ""
echo "2. 生成文章: POST /api/ai/generate-article"
echo "   - topic: 文章主题"
echo "   - outline: 文章大纲（可选）"
echo ""
echo "3. 优化提示词: POST /api/ai/optimize-prompt"
echo "   - input: 原始描述"
echo ""
echo "4. 自由文本生成: POST /api/ai/generate-text"
echo "   - prompt: 提示词"
echo "   - systemMessage: 系统消息（可选）"
echo "   - maxTokens: 最大令牌数（可选，默认1000）"
echo "   - temperature: 温度参数（可选，默认0.7）"
echo ""
echo "5. API连接测试: GET /api/ai/qwen/test"

echo -e "\n${YELLOW}API密钥配置:${NC}"
echo "通义千问API密钥: sk-4606dfde828a4f9aa7a43f5d53dddb9e"
echo "API文档: https://help.aliyun.com/document_detail/2712576.html"
