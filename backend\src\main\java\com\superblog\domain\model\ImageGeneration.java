package com.superblog.domain.model;

import java.time.LocalDateTime;

/**
 * AI图片生成领域模型
 * 表示一次AI图片生成的完整信息
 */
public class ImageGeneration {

    private Long id;
    private Long userId;
    private GenerationType generationType;
    private String prompt;
    private String negativePrompt;
    private String originalImageUrl;
    private String generatedImageUrl;
    private String thumbnailUrl;
    private Integer width;
    private Integer height;
    private String style;
    private String model;
    private Long seed;
    private Integer steps;
    private Double cfgScale;
    private GenerationStatus status;
    private String errorMessage;
    private Integer generationTime;
    private Boolean isPublic;
    private Integer viewCount;
    private Integer likeCount;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    /**
     * 生成类型枚举
     */
    public enum GenerationType {
        TEXT_TO_IMAGE("文生图"),
        IMAGE_TO_IMAGE("图生图");

        private final String description;

        GenerationType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 生成状态枚举
     */
    public enum GenerationStatus {
        PENDING("等待中"),
        PROCESSING("处理中"),
        COMPLETED("已完成"),
        FAILED("失败");

        private final String description;

        GenerationStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // 构造函数
    public ImageGeneration() {}

    public ImageGeneration(Long userId, GenerationType generationType, String prompt) {
        this.userId = userId;
        this.generationType = generationType;
        this.prompt = prompt;
        this.status = GenerationStatus.PENDING;
        this.isPublic = true;
        this.viewCount = 0;
        this.likeCount = 0;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 标记为处理中
     */
    public void markAsProcessing() {
        this.status = GenerationStatus.PROCESSING;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 标记为完成
     */
    public void markAsCompleted(String generatedImageUrl, String thumbnailUrl, Integer generationTime) {
        this.status = GenerationStatus.COMPLETED;
        this.generatedImageUrl = generatedImageUrl;
        this.thumbnailUrl = thumbnailUrl;
        this.generationTime = generationTime;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 标记为失败
     */
    public void markAsFailed(String errorMessage) {
        this.status = GenerationStatus.FAILED;
        this.errorMessage = errorMessage;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 增加查看次数
     */
    public void incrementViewCount() {
        this.viewCount = (this.viewCount == null ? 0 : this.viewCount) + 1;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 增加点赞次数
     */
    public void incrementLikeCount() {
        this.likeCount = (this.likeCount == null ? 0 : this.likeCount) + 1;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 检查是否已完成
     */
    public boolean isCompleted() {
        return GenerationStatus.COMPLETED.equals(this.status);
    }

    /**
     * 检查是否失败
     */
    public boolean isFailed() {
        return GenerationStatus.FAILED.equals(this.status);
    }

    /**
     * 检查是否正在处理
     */
    public boolean isProcessing() {
        return GenerationStatus.PROCESSING.equals(this.status);
    }

    // Getter和Setter方法
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public Long getUserId() { return userId; }
    public void setUserId(Long userId) { this.userId = userId; }

    public GenerationType getGenerationType() { return generationType; }
    public void setGenerationType(GenerationType generationType) { this.generationType = generationType; }

    public String getPrompt() { return prompt; }
    public void setPrompt(String prompt) { this.prompt = prompt; }

    public String getNegativePrompt() { return negativePrompt; }
    public void setNegativePrompt(String negativePrompt) { this.negativePrompt = negativePrompt; }

    public String getOriginalImageUrl() { return originalImageUrl; }
    public void setOriginalImageUrl(String originalImageUrl) { this.originalImageUrl = originalImageUrl; }

    public String getGeneratedImageUrl() { return generatedImageUrl; }
    public void setGeneratedImageUrl(String generatedImageUrl) { this.generatedImageUrl = generatedImageUrl; }

    public String getThumbnailUrl() { return thumbnailUrl; }
    public void setThumbnailUrl(String thumbnailUrl) { this.thumbnailUrl = thumbnailUrl; }

    public Integer getWidth() { return width; }
    public void setWidth(Integer width) { this.width = width; }

    public Integer getHeight() { return height; }
    public void setHeight(Integer height) { this.height = height; }

    public String getStyle() { return style; }
    public void setStyle(String style) { this.style = style; }

    public String getModel() { return model; }
    public void setModel(String model) { this.model = model; }

    public Long getSeed() { return seed; }
    public void setSeed(Long seed) { this.seed = seed; }

    public Integer getSteps() { return steps; }
    public void setSteps(Integer steps) { this.steps = steps; }

    public Double getCfgScale() { return cfgScale; }
    public void setCfgScale(Double cfgScale) { this.cfgScale = cfgScale; }

    public GenerationStatus getStatus() { return status; }
    public void setStatus(GenerationStatus status) { this.status = status; }

    public String getErrorMessage() { return errorMessage; }
    public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }

    public Integer getGenerationTime() { return generationTime; }
    public void setGenerationTime(Integer generationTime) { this.generationTime = generationTime; }

    public Boolean getIsPublic() { return isPublic; }
    public void setIsPublic(Boolean isPublic) { this.isPublic = isPublic; }

    public Integer getViewCount() { return viewCount; }
    public void setViewCount(Integer viewCount) { this.viewCount = viewCount; }

    public Integer getLikeCount() { return likeCount; }
    public void setLikeCount(Integer likeCount) { this.likeCount = likeCount; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
}
