"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "abap", {
  enumerable: true,
  get: function get() {
    return _abap["default"];
  }
});
Object.defineProperty(exports, "abnf", {
  enumerable: true,
  get: function get() {
    return _abnf["default"];
  }
});
Object.defineProperty(exports, "actionscript", {
  enumerable: true,
  get: function get() {
    return _actionscript["default"];
  }
});
Object.defineProperty(exports, "ada", {
  enumerable: true,
  get: function get() {
    return _ada["default"];
  }
});
Object.defineProperty(exports, "agda", {
  enumerable: true,
  get: function get() {
    return _agda["default"];
  }
});
Object.defineProperty(exports, "al", {
  enumerable: true,
  get: function get() {
    return _al["default"];
  }
});
Object.defineProperty(exports, "antlr4", {
  enumerable: true,
  get: function get() {
    return _antlr["default"];
  }
});
Object.defineProperty(exports, "apacheconf", {
  enumerable: true,
  get: function get() {
    return _apacheconf["default"];
  }
});
Object.defineProperty(exports, "apex", {
  enumerable: true,
  get: function get() {
    return _apex["default"];
  }
});
Object.defineProperty(exports, "apl", {
  enumerable: true,
  get: function get() {
    return _apl["default"];
  }
});
Object.defineProperty(exports, "applescript", {
  enumerable: true,
  get: function get() {
    return _applescript["default"];
  }
});
Object.defineProperty(exports, "aql", {
  enumerable: true,
  get: function get() {
    return _aql["default"];
  }
});
Object.defineProperty(exports, "arduino", {
  enumerable: true,
  get: function get() {
    return _arduino["default"];
  }
});
Object.defineProperty(exports, "arff", {
  enumerable: true,
  get: function get() {
    return _arff["default"];
  }
});
Object.defineProperty(exports, "asciidoc", {
  enumerable: true,
  get: function get() {
    return _asciidoc["default"];
  }
});
Object.defineProperty(exports, "asm6502", {
  enumerable: true,
  get: function get() {
    return _asm["default"];
  }
});
Object.defineProperty(exports, "asmatmel", {
  enumerable: true,
  get: function get() {
    return _asmatmel["default"];
  }
});
Object.defineProperty(exports, "aspnet", {
  enumerable: true,
  get: function get() {
    return _aspnet["default"];
  }
});
Object.defineProperty(exports, "autohotkey", {
  enumerable: true,
  get: function get() {
    return _autohotkey["default"];
  }
});
Object.defineProperty(exports, "autoit", {
  enumerable: true,
  get: function get() {
    return _autoit["default"];
  }
});
Object.defineProperty(exports, "avisynth", {
  enumerable: true,
  get: function get() {
    return _avisynth["default"];
  }
});
Object.defineProperty(exports, "avroIdl", {
  enumerable: true,
  get: function get() {
    return _avroIdl["default"];
  }
});
Object.defineProperty(exports, "bash", {
  enumerable: true,
  get: function get() {
    return _bash["default"];
  }
});
Object.defineProperty(exports, "basic", {
  enumerable: true,
  get: function get() {
    return _basic["default"];
  }
});
Object.defineProperty(exports, "batch", {
  enumerable: true,
  get: function get() {
    return _batch["default"];
  }
});
Object.defineProperty(exports, "bbcode", {
  enumerable: true,
  get: function get() {
    return _bbcode["default"];
  }
});
Object.defineProperty(exports, "bicep", {
  enumerable: true,
  get: function get() {
    return _bicep["default"];
  }
});
Object.defineProperty(exports, "birb", {
  enumerable: true,
  get: function get() {
    return _birb["default"];
  }
});
Object.defineProperty(exports, "bison", {
  enumerable: true,
  get: function get() {
    return _bison["default"];
  }
});
Object.defineProperty(exports, "bnf", {
  enumerable: true,
  get: function get() {
    return _bnf["default"];
  }
});
Object.defineProperty(exports, "brainfuck", {
  enumerable: true,
  get: function get() {
    return _brainfuck["default"];
  }
});
Object.defineProperty(exports, "brightscript", {
  enumerable: true,
  get: function get() {
    return _brightscript["default"];
  }
});
Object.defineProperty(exports, "bro", {
  enumerable: true,
  get: function get() {
    return _bro["default"];
  }
});
Object.defineProperty(exports, "bsl", {
  enumerable: true,
  get: function get() {
    return _bsl["default"];
  }
});
Object.defineProperty(exports, "c", {
  enumerable: true,
  get: function get() {
    return _c["default"];
  }
});
Object.defineProperty(exports, "cfscript", {
  enumerable: true,
  get: function get() {
    return _cfscript["default"];
  }
});
Object.defineProperty(exports, "chaiscript", {
  enumerable: true,
  get: function get() {
    return _chaiscript["default"];
  }
});
Object.defineProperty(exports, "cil", {
  enumerable: true,
  get: function get() {
    return _cil["default"];
  }
});
Object.defineProperty(exports, "clike", {
  enumerable: true,
  get: function get() {
    return _clike["default"];
  }
});
Object.defineProperty(exports, "clojure", {
  enumerable: true,
  get: function get() {
    return _clojure["default"];
  }
});
Object.defineProperty(exports, "cmake", {
  enumerable: true,
  get: function get() {
    return _cmake["default"];
  }
});
Object.defineProperty(exports, "cobol", {
  enumerable: true,
  get: function get() {
    return _cobol["default"];
  }
});
Object.defineProperty(exports, "coffeescript", {
  enumerable: true,
  get: function get() {
    return _coffeescript["default"];
  }
});
Object.defineProperty(exports, "concurnas", {
  enumerable: true,
  get: function get() {
    return _concurnas["default"];
  }
});
Object.defineProperty(exports, "coq", {
  enumerable: true,
  get: function get() {
    return _coq["default"];
  }
});
Object.defineProperty(exports, "cpp", {
  enumerable: true,
  get: function get() {
    return _cpp["default"];
  }
});
Object.defineProperty(exports, "crystal", {
  enumerable: true,
  get: function get() {
    return _crystal["default"];
  }
});
Object.defineProperty(exports, "csharp", {
  enumerable: true,
  get: function get() {
    return _csharp["default"];
  }
});
Object.defineProperty(exports, "cshtml", {
  enumerable: true,
  get: function get() {
    return _cshtml["default"];
  }
});
Object.defineProperty(exports, "csp", {
  enumerable: true,
  get: function get() {
    return _csp["default"];
  }
});
Object.defineProperty(exports, "css", {
  enumerable: true,
  get: function get() {
    return _css["default"];
  }
});
Object.defineProperty(exports, "cssExtras", {
  enumerable: true,
  get: function get() {
    return _cssExtras["default"];
  }
});
Object.defineProperty(exports, "csv", {
  enumerable: true,
  get: function get() {
    return _csv["default"];
  }
});
Object.defineProperty(exports, "cypher", {
  enumerable: true,
  get: function get() {
    return _cypher["default"];
  }
});
Object.defineProperty(exports, "d", {
  enumerable: true,
  get: function get() {
    return _d["default"];
  }
});
Object.defineProperty(exports, "dart", {
  enumerable: true,
  get: function get() {
    return _dart["default"];
  }
});
Object.defineProperty(exports, "dataweave", {
  enumerable: true,
  get: function get() {
    return _dataweave["default"];
  }
});
Object.defineProperty(exports, "dax", {
  enumerable: true,
  get: function get() {
    return _dax["default"];
  }
});
Object.defineProperty(exports, "dhall", {
  enumerable: true,
  get: function get() {
    return _dhall["default"];
  }
});
Object.defineProperty(exports, "diff", {
  enumerable: true,
  get: function get() {
    return _diff["default"];
  }
});
Object.defineProperty(exports, "django", {
  enumerable: true,
  get: function get() {
    return _django["default"];
  }
});
Object.defineProperty(exports, "dnsZoneFile", {
  enumerable: true,
  get: function get() {
    return _dnsZoneFile["default"];
  }
});
Object.defineProperty(exports, "docker", {
  enumerable: true,
  get: function get() {
    return _docker["default"];
  }
});
Object.defineProperty(exports, "dot", {
  enumerable: true,
  get: function get() {
    return _dot["default"];
  }
});
Object.defineProperty(exports, "ebnf", {
  enumerable: true,
  get: function get() {
    return _ebnf["default"];
  }
});
Object.defineProperty(exports, "editorconfig", {
  enumerable: true,
  get: function get() {
    return _editorconfig["default"];
  }
});
Object.defineProperty(exports, "eiffel", {
  enumerable: true,
  get: function get() {
    return _eiffel["default"];
  }
});
Object.defineProperty(exports, "ejs", {
  enumerable: true,
  get: function get() {
    return _ejs["default"];
  }
});
Object.defineProperty(exports, "elixir", {
  enumerable: true,
  get: function get() {
    return _elixir["default"];
  }
});
Object.defineProperty(exports, "elm", {
  enumerable: true,
  get: function get() {
    return _elm["default"];
  }
});
Object.defineProperty(exports, "erb", {
  enumerable: true,
  get: function get() {
    return _erb["default"];
  }
});
Object.defineProperty(exports, "erlang", {
  enumerable: true,
  get: function get() {
    return _erlang["default"];
  }
});
Object.defineProperty(exports, "etlua", {
  enumerable: true,
  get: function get() {
    return _etlua["default"];
  }
});
Object.defineProperty(exports, "excelFormula", {
  enumerable: true,
  get: function get() {
    return _excelFormula["default"];
  }
});
Object.defineProperty(exports, "factor", {
  enumerable: true,
  get: function get() {
    return _factor["default"];
  }
});
Object.defineProperty(exports, "falselang", {
  enumerable: true,
  get: function get() {
    return _false["default"];
  }
});
Object.defineProperty(exports, "firestoreSecurityRules", {
  enumerable: true,
  get: function get() {
    return _firestoreSecurityRules["default"];
  }
});
Object.defineProperty(exports, "flow", {
  enumerable: true,
  get: function get() {
    return _flow["default"];
  }
});
Object.defineProperty(exports, "fortran", {
  enumerable: true,
  get: function get() {
    return _fortran["default"];
  }
});
Object.defineProperty(exports, "fsharp", {
  enumerable: true,
  get: function get() {
    return _fsharp["default"];
  }
});
Object.defineProperty(exports, "ftl", {
  enumerable: true,
  get: function get() {
    return _ftl["default"];
  }
});
Object.defineProperty(exports, "gap", {
  enumerable: true,
  get: function get() {
    return _gap["default"];
  }
});
Object.defineProperty(exports, "gcode", {
  enumerable: true,
  get: function get() {
    return _gcode["default"];
  }
});
Object.defineProperty(exports, "gdscript", {
  enumerable: true,
  get: function get() {
    return _gdscript["default"];
  }
});
Object.defineProperty(exports, "gedcom", {
  enumerable: true,
  get: function get() {
    return _gedcom["default"];
  }
});
Object.defineProperty(exports, "gherkin", {
  enumerable: true,
  get: function get() {
    return _gherkin["default"];
  }
});
Object.defineProperty(exports, "git", {
  enumerable: true,
  get: function get() {
    return _git["default"];
  }
});
Object.defineProperty(exports, "glsl", {
  enumerable: true,
  get: function get() {
    return _glsl["default"];
  }
});
Object.defineProperty(exports, "gml", {
  enumerable: true,
  get: function get() {
    return _gml["default"];
  }
});
Object.defineProperty(exports, "gn", {
  enumerable: true,
  get: function get() {
    return _gn["default"];
  }
});
Object.defineProperty(exports, "go", {
  enumerable: true,
  get: function get() {
    return _go["default"];
  }
});
Object.defineProperty(exports, "goModule", {
  enumerable: true,
  get: function get() {
    return _goModule["default"];
  }
});
Object.defineProperty(exports, "graphql", {
  enumerable: true,
  get: function get() {
    return _graphql["default"];
  }
});
Object.defineProperty(exports, "groovy", {
  enumerable: true,
  get: function get() {
    return _groovy["default"];
  }
});
Object.defineProperty(exports, "haml", {
  enumerable: true,
  get: function get() {
    return _haml["default"];
  }
});
Object.defineProperty(exports, "handlebars", {
  enumerable: true,
  get: function get() {
    return _handlebars["default"];
  }
});
Object.defineProperty(exports, "haskell", {
  enumerable: true,
  get: function get() {
    return _haskell["default"];
  }
});
Object.defineProperty(exports, "haxe", {
  enumerable: true,
  get: function get() {
    return _haxe["default"];
  }
});
Object.defineProperty(exports, "hcl", {
  enumerable: true,
  get: function get() {
    return _hcl["default"];
  }
});
Object.defineProperty(exports, "hlsl", {
  enumerable: true,
  get: function get() {
    return _hlsl["default"];
  }
});
Object.defineProperty(exports, "hoon", {
  enumerable: true,
  get: function get() {
    return _hoon["default"];
  }
});
Object.defineProperty(exports, "hpkp", {
  enumerable: true,
  get: function get() {
    return _hpkp["default"];
  }
});
Object.defineProperty(exports, "hsts", {
  enumerable: true,
  get: function get() {
    return _hsts["default"];
  }
});
Object.defineProperty(exports, "http", {
  enumerable: true,
  get: function get() {
    return _http["default"];
  }
});
Object.defineProperty(exports, "ichigojam", {
  enumerable: true,
  get: function get() {
    return _ichigojam["default"];
  }
});
Object.defineProperty(exports, "icon", {
  enumerable: true,
  get: function get() {
    return _icon["default"];
  }
});
Object.defineProperty(exports, "icuMessageFormat", {
  enumerable: true,
  get: function get() {
    return _icuMessageFormat["default"];
  }
});
Object.defineProperty(exports, "idris", {
  enumerable: true,
  get: function get() {
    return _idris["default"];
  }
});
Object.defineProperty(exports, "iecst", {
  enumerable: true,
  get: function get() {
    return _iecst["default"];
  }
});
Object.defineProperty(exports, "ignore", {
  enumerable: true,
  get: function get() {
    return _ignore["default"];
  }
});
Object.defineProperty(exports, "inform7", {
  enumerable: true,
  get: function get() {
    return _inform["default"];
  }
});
Object.defineProperty(exports, "ini", {
  enumerable: true,
  get: function get() {
    return _ini["default"];
  }
});
Object.defineProperty(exports, "io", {
  enumerable: true,
  get: function get() {
    return _io["default"];
  }
});
Object.defineProperty(exports, "j", {
  enumerable: true,
  get: function get() {
    return _j["default"];
  }
});
Object.defineProperty(exports, "java", {
  enumerable: true,
  get: function get() {
    return _java["default"];
  }
});
Object.defineProperty(exports, "javadoc", {
  enumerable: true,
  get: function get() {
    return _javadoc["default"];
  }
});
Object.defineProperty(exports, "javadoclike", {
  enumerable: true,
  get: function get() {
    return _javadoclike["default"];
  }
});
Object.defineProperty(exports, "javascript", {
  enumerable: true,
  get: function get() {
    return _javascript["default"];
  }
});
Object.defineProperty(exports, "javastacktrace", {
  enumerable: true,
  get: function get() {
    return _javastacktrace["default"];
  }
});
Object.defineProperty(exports, "jexl", {
  enumerable: true,
  get: function get() {
    return _jexl["default"];
  }
});
Object.defineProperty(exports, "jolie", {
  enumerable: true,
  get: function get() {
    return _jolie["default"];
  }
});
Object.defineProperty(exports, "jq", {
  enumerable: true,
  get: function get() {
    return _jq["default"];
  }
});
Object.defineProperty(exports, "jsExtras", {
  enumerable: true,
  get: function get() {
    return _jsExtras["default"];
  }
});
Object.defineProperty(exports, "jsTemplates", {
  enumerable: true,
  get: function get() {
    return _jsTemplates["default"];
  }
});
Object.defineProperty(exports, "jsdoc", {
  enumerable: true,
  get: function get() {
    return _jsdoc["default"];
  }
});
Object.defineProperty(exports, "json", {
  enumerable: true,
  get: function get() {
    return _json["default"];
  }
});
Object.defineProperty(exports, "json5", {
  enumerable: true,
  get: function get() {
    return _json2["default"];
  }
});
Object.defineProperty(exports, "jsonp", {
  enumerable: true,
  get: function get() {
    return _jsonp["default"];
  }
});
Object.defineProperty(exports, "jsstacktrace", {
  enumerable: true,
  get: function get() {
    return _jsstacktrace["default"];
  }
});
Object.defineProperty(exports, "jsx", {
  enumerable: true,
  get: function get() {
    return _jsx["default"];
  }
});
Object.defineProperty(exports, "julia", {
  enumerable: true,
  get: function get() {
    return _julia["default"];
  }
});
Object.defineProperty(exports, "keepalived", {
  enumerable: true,
  get: function get() {
    return _keepalived["default"];
  }
});
Object.defineProperty(exports, "keyman", {
  enumerable: true,
  get: function get() {
    return _keyman["default"];
  }
});
Object.defineProperty(exports, "kotlin", {
  enumerable: true,
  get: function get() {
    return _kotlin["default"];
  }
});
Object.defineProperty(exports, "kumir", {
  enumerable: true,
  get: function get() {
    return _kumir["default"];
  }
});
Object.defineProperty(exports, "kusto", {
  enumerable: true,
  get: function get() {
    return _kusto["default"];
  }
});
Object.defineProperty(exports, "latex", {
  enumerable: true,
  get: function get() {
    return _latex["default"];
  }
});
Object.defineProperty(exports, "latte", {
  enumerable: true,
  get: function get() {
    return _latte["default"];
  }
});
Object.defineProperty(exports, "less", {
  enumerable: true,
  get: function get() {
    return _less["default"];
  }
});
Object.defineProperty(exports, "lilypond", {
  enumerable: true,
  get: function get() {
    return _lilypond["default"];
  }
});
Object.defineProperty(exports, "liquid", {
  enumerable: true,
  get: function get() {
    return _liquid["default"];
  }
});
Object.defineProperty(exports, "lisp", {
  enumerable: true,
  get: function get() {
    return _lisp["default"];
  }
});
Object.defineProperty(exports, "livescript", {
  enumerable: true,
  get: function get() {
    return _livescript["default"];
  }
});
Object.defineProperty(exports, "llvm", {
  enumerable: true,
  get: function get() {
    return _llvm["default"];
  }
});
Object.defineProperty(exports, "log", {
  enumerable: true,
  get: function get() {
    return _log["default"];
  }
});
Object.defineProperty(exports, "lolcode", {
  enumerable: true,
  get: function get() {
    return _lolcode["default"];
  }
});
Object.defineProperty(exports, "lua", {
  enumerable: true,
  get: function get() {
    return _lua["default"];
  }
});
Object.defineProperty(exports, "magma", {
  enumerable: true,
  get: function get() {
    return _magma["default"];
  }
});
Object.defineProperty(exports, "makefile", {
  enumerable: true,
  get: function get() {
    return _makefile["default"];
  }
});
Object.defineProperty(exports, "markdown", {
  enumerable: true,
  get: function get() {
    return _markdown["default"];
  }
});
Object.defineProperty(exports, "markup", {
  enumerable: true,
  get: function get() {
    return _markup["default"];
  }
});
Object.defineProperty(exports, "markupTemplating", {
  enumerable: true,
  get: function get() {
    return _markupTemplating["default"];
  }
});
Object.defineProperty(exports, "matlab", {
  enumerable: true,
  get: function get() {
    return _matlab["default"];
  }
});
Object.defineProperty(exports, "maxscript", {
  enumerable: true,
  get: function get() {
    return _maxscript["default"];
  }
});
Object.defineProperty(exports, "mel", {
  enumerable: true,
  get: function get() {
    return _mel["default"];
  }
});
Object.defineProperty(exports, "mermaid", {
  enumerable: true,
  get: function get() {
    return _mermaid["default"];
  }
});
Object.defineProperty(exports, "mizar", {
  enumerable: true,
  get: function get() {
    return _mizar["default"];
  }
});
Object.defineProperty(exports, "mongodb", {
  enumerable: true,
  get: function get() {
    return _mongodb["default"];
  }
});
Object.defineProperty(exports, "monkey", {
  enumerable: true,
  get: function get() {
    return _monkey["default"];
  }
});
Object.defineProperty(exports, "moonscript", {
  enumerable: true,
  get: function get() {
    return _moonscript["default"];
  }
});
Object.defineProperty(exports, "n1ql", {
  enumerable: true,
  get: function get() {
    return _n1ql["default"];
  }
});
Object.defineProperty(exports, "n4js", {
  enumerable: true,
  get: function get() {
    return _n4js["default"];
  }
});
Object.defineProperty(exports, "nand2tetrisHdl", {
  enumerable: true,
  get: function get() {
    return _nand2tetrisHdl["default"];
  }
});
Object.defineProperty(exports, "naniscript", {
  enumerable: true,
  get: function get() {
    return _naniscript["default"];
  }
});
Object.defineProperty(exports, "nasm", {
  enumerable: true,
  get: function get() {
    return _nasm["default"];
  }
});
Object.defineProperty(exports, "neon", {
  enumerable: true,
  get: function get() {
    return _neon["default"];
  }
});
Object.defineProperty(exports, "nevod", {
  enumerable: true,
  get: function get() {
    return _nevod["default"];
  }
});
Object.defineProperty(exports, "nginx", {
  enumerable: true,
  get: function get() {
    return _nginx["default"];
  }
});
Object.defineProperty(exports, "nim", {
  enumerable: true,
  get: function get() {
    return _nim["default"];
  }
});
Object.defineProperty(exports, "nix", {
  enumerable: true,
  get: function get() {
    return _nix["default"];
  }
});
Object.defineProperty(exports, "nsis", {
  enumerable: true,
  get: function get() {
    return _nsis["default"];
  }
});
Object.defineProperty(exports, "objectivec", {
  enumerable: true,
  get: function get() {
    return _objectivec["default"];
  }
});
Object.defineProperty(exports, "ocaml", {
  enumerable: true,
  get: function get() {
    return _ocaml["default"];
  }
});
Object.defineProperty(exports, "opencl", {
  enumerable: true,
  get: function get() {
    return _opencl["default"];
  }
});
Object.defineProperty(exports, "openqasm", {
  enumerable: true,
  get: function get() {
    return _openqasm["default"];
  }
});
Object.defineProperty(exports, "oz", {
  enumerable: true,
  get: function get() {
    return _oz["default"];
  }
});
Object.defineProperty(exports, "parigp", {
  enumerable: true,
  get: function get() {
    return _parigp["default"];
  }
});
Object.defineProperty(exports, "parser", {
  enumerable: true,
  get: function get() {
    return _parser["default"];
  }
});
Object.defineProperty(exports, "pascal", {
  enumerable: true,
  get: function get() {
    return _pascal["default"];
  }
});
Object.defineProperty(exports, "pascaligo", {
  enumerable: true,
  get: function get() {
    return _pascaligo["default"];
  }
});
Object.defineProperty(exports, "pcaxis", {
  enumerable: true,
  get: function get() {
    return _pcaxis["default"];
  }
});
Object.defineProperty(exports, "peoplecode", {
  enumerable: true,
  get: function get() {
    return _peoplecode["default"];
  }
});
Object.defineProperty(exports, "perl", {
  enumerable: true,
  get: function get() {
    return _perl["default"];
  }
});
Object.defineProperty(exports, "php", {
  enumerable: true,
  get: function get() {
    return _php["default"];
  }
});
Object.defineProperty(exports, "phpExtras", {
  enumerable: true,
  get: function get() {
    return _phpExtras["default"];
  }
});
Object.defineProperty(exports, "phpdoc", {
  enumerable: true,
  get: function get() {
    return _phpdoc["default"];
  }
});
Object.defineProperty(exports, "plsql", {
  enumerable: true,
  get: function get() {
    return _plsql["default"];
  }
});
Object.defineProperty(exports, "powerquery", {
  enumerable: true,
  get: function get() {
    return _powerquery["default"];
  }
});
Object.defineProperty(exports, "powershell", {
  enumerable: true,
  get: function get() {
    return _powershell["default"];
  }
});
Object.defineProperty(exports, "processing", {
  enumerable: true,
  get: function get() {
    return _processing["default"];
  }
});
Object.defineProperty(exports, "prolog", {
  enumerable: true,
  get: function get() {
    return _prolog["default"];
  }
});
Object.defineProperty(exports, "promql", {
  enumerable: true,
  get: function get() {
    return _promql["default"];
  }
});
Object.defineProperty(exports, "properties", {
  enumerable: true,
  get: function get() {
    return _properties["default"];
  }
});
Object.defineProperty(exports, "protobuf", {
  enumerable: true,
  get: function get() {
    return _protobuf["default"];
  }
});
Object.defineProperty(exports, "psl", {
  enumerable: true,
  get: function get() {
    return _psl["default"];
  }
});
Object.defineProperty(exports, "pug", {
  enumerable: true,
  get: function get() {
    return _pug["default"];
  }
});
Object.defineProperty(exports, "puppet", {
  enumerable: true,
  get: function get() {
    return _puppet["default"];
  }
});
Object.defineProperty(exports, "pure", {
  enumerable: true,
  get: function get() {
    return _pure["default"];
  }
});
Object.defineProperty(exports, "purebasic", {
  enumerable: true,
  get: function get() {
    return _purebasic["default"];
  }
});
Object.defineProperty(exports, "purescript", {
  enumerable: true,
  get: function get() {
    return _purescript["default"];
  }
});
Object.defineProperty(exports, "python", {
  enumerable: true,
  get: function get() {
    return _python["default"];
  }
});
Object.defineProperty(exports, "q", {
  enumerable: true,
  get: function get() {
    return _q["default"];
  }
});
Object.defineProperty(exports, "qml", {
  enumerable: true,
  get: function get() {
    return _qml["default"];
  }
});
Object.defineProperty(exports, "qore", {
  enumerable: true,
  get: function get() {
    return _qore["default"];
  }
});
Object.defineProperty(exports, "qsharp", {
  enumerable: true,
  get: function get() {
    return _qsharp["default"];
  }
});
Object.defineProperty(exports, "r", {
  enumerable: true,
  get: function get() {
    return _r["default"];
  }
});
Object.defineProperty(exports, "racket", {
  enumerable: true,
  get: function get() {
    return _racket["default"];
  }
});
Object.defineProperty(exports, "reason", {
  enumerable: true,
  get: function get() {
    return _reason["default"];
  }
});
Object.defineProperty(exports, "regex", {
  enumerable: true,
  get: function get() {
    return _regex["default"];
  }
});
Object.defineProperty(exports, "rego", {
  enumerable: true,
  get: function get() {
    return _rego["default"];
  }
});
Object.defineProperty(exports, "renpy", {
  enumerable: true,
  get: function get() {
    return _renpy["default"];
  }
});
Object.defineProperty(exports, "rest", {
  enumerable: true,
  get: function get() {
    return _rest["default"];
  }
});
Object.defineProperty(exports, "rip", {
  enumerable: true,
  get: function get() {
    return _rip["default"];
  }
});
Object.defineProperty(exports, "roboconf", {
  enumerable: true,
  get: function get() {
    return _roboconf["default"];
  }
});
Object.defineProperty(exports, "robotframework", {
  enumerable: true,
  get: function get() {
    return _robotframework["default"];
  }
});
Object.defineProperty(exports, "ruby", {
  enumerable: true,
  get: function get() {
    return _ruby["default"];
  }
});
Object.defineProperty(exports, "rust", {
  enumerable: true,
  get: function get() {
    return _rust["default"];
  }
});
Object.defineProperty(exports, "sas", {
  enumerable: true,
  get: function get() {
    return _sas["default"];
  }
});
Object.defineProperty(exports, "sass", {
  enumerable: true,
  get: function get() {
    return _sass["default"];
  }
});
Object.defineProperty(exports, "scala", {
  enumerable: true,
  get: function get() {
    return _scala["default"];
  }
});
Object.defineProperty(exports, "scheme", {
  enumerable: true,
  get: function get() {
    return _scheme["default"];
  }
});
Object.defineProperty(exports, "scss", {
  enumerable: true,
  get: function get() {
    return _scss["default"];
  }
});
Object.defineProperty(exports, "shellSession", {
  enumerable: true,
  get: function get() {
    return _shellSession["default"];
  }
});
Object.defineProperty(exports, "smali", {
  enumerable: true,
  get: function get() {
    return _smali["default"];
  }
});
Object.defineProperty(exports, "smalltalk", {
  enumerable: true,
  get: function get() {
    return _smalltalk["default"];
  }
});
Object.defineProperty(exports, "smarty", {
  enumerable: true,
  get: function get() {
    return _smarty["default"];
  }
});
Object.defineProperty(exports, "sml", {
  enumerable: true,
  get: function get() {
    return _sml["default"];
  }
});
Object.defineProperty(exports, "solidity", {
  enumerable: true,
  get: function get() {
    return _solidity["default"];
  }
});
Object.defineProperty(exports, "solutionFile", {
  enumerable: true,
  get: function get() {
    return _solutionFile["default"];
  }
});
Object.defineProperty(exports, "soy", {
  enumerable: true,
  get: function get() {
    return _soy["default"];
  }
});
Object.defineProperty(exports, "sparql", {
  enumerable: true,
  get: function get() {
    return _sparql["default"];
  }
});
Object.defineProperty(exports, "splunkSpl", {
  enumerable: true,
  get: function get() {
    return _splunkSpl["default"];
  }
});
Object.defineProperty(exports, "sqf", {
  enumerable: true,
  get: function get() {
    return _sqf["default"];
  }
});
Object.defineProperty(exports, "sql", {
  enumerable: true,
  get: function get() {
    return _sql["default"];
  }
});
Object.defineProperty(exports, "squirrel", {
  enumerable: true,
  get: function get() {
    return _squirrel["default"];
  }
});
Object.defineProperty(exports, "stan", {
  enumerable: true,
  get: function get() {
    return _stan["default"];
  }
});
Object.defineProperty(exports, "stylus", {
  enumerable: true,
  get: function get() {
    return _stylus["default"];
  }
});
Object.defineProperty(exports, "swift", {
  enumerable: true,
  get: function get() {
    return _swift["default"];
  }
});
Object.defineProperty(exports, "systemd", {
  enumerable: true,
  get: function get() {
    return _systemd["default"];
  }
});
Object.defineProperty(exports, "t4Cs", {
  enumerable: true,
  get: function get() {
    return _t4Cs["default"];
  }
});
Object.defineProperty(exports, "t4Templating", {
  enumerable: true,
  get: function get() {
    return _t4Templating["default"];
  }
});
Object.defineProperty(exports, "t4Vb", {
  enumerable: true,
  get: function get() {
    return _t4Vb["default"];
  }
});
Object.defineProperty(exports, "tap", {
  enumerable: true,
  get: function get() {
    return _tap["default"];
  }
});
Object.defineProperty(exports, "tcl", {
  enumerable: true,
  get: function get() {
    return _tcl["default"];
  }
});
Object.defineProperty(exports, "textile", {
  enumerable: true,
  get: function get() {
    return _textile["default"];
  }
});
Object.defineProperty(exports, "toml", {
  enumerable: true,
  get: function get() {
    return _toml["default"];
  }
});
Object.defineProperty(exports, "tremor", {
  enumerable: true,
  get: function get() {
    return _tremor["default"];
  }
});
Object.defineProperty(exports, "tsx", {
  enumerable: true,
  get: function get() {
    return _tsx["default"];
  }
});
Object.defineProperty(exports, "tt2", {
  enumerable: true,
  get: function get() {
    return _tt["default"];
  }
});
Object.defineProperty(exports, "turtle", {
  enumerable: true,
  get: function get() {
    return _turtle["default"];
  }
});
Object.defineProperty(exports, "twig", {
  enumerable: true,
  get: function get() {
    return _twig["default"];
  }
});
Object.defineProperty(exports, "typescript", {
  enumerable: true,
  get: function get() {
    return _typescript["default"];
  }
});
Object.defineProperty(exports, "typoscript", {
  enumerable: true,
  get: function get() {
    return _typoscript["default"];
  }
});
Object.defineProperty(exports, "unrealscript", {
  enumerable: true,
  get: function get() {
    return _unrealscript["default"];
  }
});
Object.defineProperty(exports, "uorazor", {
  enumerable: true,
  get: function get() {
    return _uorazor["default"];
  }
});
Object.defineProperty(exports, "uri", {
  enumerable: true,
  get: function get() {
    return _uri["default"];
  }
});
Object.defineProperty(exports, "v", {
  enumerable: true,
  get: function get() {
    return _v["default"];
  }
});
Object.defineProperty(exports, "vala", {
  enumerable: true,
  get: function get() {
    return _vala["default"];
  }
});
Object.defineProperty(exports, "vbnet", {
  enumerable: true,
  get: function get() {
    return _vbnet["default"];
  }
});
Object.defineProperty(exports, "velocity", {
  enumerable: true,
  get: function get() {
    return _velocity["default"];
  }
});
Object.defineProperty(exports, "verilog", {
  enumerable: true,
  get: function get() {
    return _verilog["default"];
  }
});
Object.defineProperty(exports, "vhdl", {
  enumerable: true,
  get: function get() {
    return _vhdl["default"];
  }
});
Object.defineProperty(exports, "vim", {
  enumerable: true,
  get: function get() {
    return _vim["default"];
  }
});
Object.defineProperty(exports, "visualBasic", {
  enumerable: true,
  get: function get() {
    return _visualBasic["default"];
  }
});
Object.defineProperty(exports, "warpscript", {
  enumerable: true,
  get: function get() {
    return _warpscript["default"];
  }
});
Object.defineProperty(exports, "wasm", {
  enumerable: true,
  get: function get() {
    return _wasm["default"];
  }
});
Object.defineProperty(exports, "webIdl", {
  enumerable: true,
  get: function get() {
    return _webIdl["default"];
  }
});
Object.defineProperty(exports, "wiki", {
  enumerable: true,
  get: function get() {
    return _wiki["default"];
  }
});
Object.defineProperty(exports, "wolfram", {
  enumerable: true,
  get: function get() {
    return _wolfram["default"];
  }
});
Object.defineProperty(exports, "wren", {
  enumerable: true,
  get: function get() {
    return _wren["default"];
  }
});
Object.defineProperty(exports, "xeora", {
  enumerable: true,
  get: function get() {
    return _xeora["default"];
  }
});
Object.defineProperty(exports, "xmlDoc", {
  enumerable: true,
  get: function get() {
    return _xmlDoc["default"];
  }
});
Object.defineProperty(exports, "xojo", {
  enumerable: true,
  get: function get() {
    return _xojo["default"];
  }
});
Object.defineProperty(exports, "xquery", {
  enumerable: true,
  get: function get() {
    return _xquery["default"];
  }
});
Object.defineProperty(exports, "yaml", {
  enumerable: true,
  get: function get() {
    return _yaml["default"];
  }
});
Object.defineProperty(exports, "yang", {
  enumerable: true,
  get: function get() {
    return _yang["default"];
  }
});
Object.defineProperty(exports, "zig", {
  enumerable: true,
  get: function get() {
    return _zig["default"];
  }
});
var _abap = _interopRequireDefault(require("./abap"));
var _abnf = _interopRequireDefault(require("./abnf"));
var _actionscript = _interopRequireDefault(require("./actionscript"));
var _ada = _interopRequireDefault(require("./ada"));
var _agda = _interopRequireDefault(require("./agda"));
var _al = _interopRequireDefault(require("./al"));
var _antlr = _interopRequireDefault(require("./antlr4"));
var _apacheconf = _interopRequireDefault(require("./apacheconf"));
var _apex = _interopRequireDefault(require("./apex"));
var _apl = _interopRequireDefault(require("./apl"));
var _applescript = _interopRequireDefault(require("./applescript"));
var _aql = _interopRequireDefault(require("./aql"));
var _arduino = _interopRequireDefault(require("./arduino"));
var _arff = _interopRequireDefault(require("./arff"));
var _asciidoc = _interopRequireDefault(require("./asciidoc"));
var _asm = _interopRequireDefault(require("./asm6502"));
var _asmatmel = _interopRequireDefault(require("./asmatmel"));
var _aspnet = _interopRequireDefault(require("./aspnet"));
var _autohotkey = _interopRequireDefault(require("./autohotkey"));
var _autoit = _interopRequireDefault(require("./autoit"));
var _avisynth = _interopRequireDefault(require("./avisynth"));
var _avroIdl = _interopRequireDefault(require("./avro-idl"));
var _bash = _interopRequireDefault(require("./bash"));
var _basic = _interopRequireDefault(require("./basic"));
var _batch = _interopRequireDefault(require("./batch"));
var _bbcode = _interopRequireDefault(require("./bbcode"));
var _bicep = _interopRequireDefault(require("./bicep"));
var _birb = _interopRequireDefault(require("./birb"));
var _bison = _interopRequireDefault(require("./bison"));
var _bnf = _interopRequireDefault(require("./bnf"));
var _brainfuck = _interopRequireDefault(require("./brainfuck"));
var _brightscript = _interopRequireDefault(require("./brightscript"));
var _bro = _interopRequireDefault(require("./bro"));
var _bsl = _interopRequireDefault(require("./bsl"));
var _c = _interopRequireDefault(require("./c"));
var _cfscript = _interopRequireDefault(require("./cfscript"));
var _chaiscript = _interopRequireDefault(require("./chaiscript"));
var _cil = _interopRequireDefault(require("./cil"));
var _clike = _interopRequireDefault(require("./clike"));
var _clojure = _interopRequireDefault(require("./clojure"));
var _cmake = _interopRequireDefault(require("./cmake"));
var _cobol = _interopRequireDefault(require("./cobol"));
var _coffeescript = _interopRequireDefault(require("./coffeescript"));
var _concurnas = _interopRequireDefault(require("./concurnas"));
var _coq = _interopRequireDefault(require("./coq"));
var _cpp = _interopRequireDefault(require("./cpp"));
var _crystal = _interopRequireDefault(require("./crystal"));
var _csharp = _interopRequireDefault(require("./csharp"));
var _cshtml = _interopRequireDefault(require("./cshtml"));
var _csp = _interopRequireDefault(require("./csp"));
var _cssExtras = _interopRequireDefault(require("./css-extras"));
var _css = _interopRequireDefault(require("./css"));
var _csv = _interopRequireDefault(require("./csv"));
var _cypher = _interopRequireDefault(require("./cypher"));
var _d = _interopRequireDefault(require("./d"));
var _dart = _interopRequireDefault(require("./dart"));
var _dataweave = _interopRequireDefault(require("./dataweave"));
var _dax = _interopRequireDefault(require("./dax"));
var _dhall = _interopRequireDefault(require("./dhall"));
var _diff = _interopRequireDefault(require("./diff"));
var _django = _interopRequireDefault(require("./django"));
var _dnsZoneFile = _interopRequireDefault(require("./dns-zone-file"));
var _docker = _interopRequireDefault(require("./docker"));
var _dot = _interopRequireDefault(require("./dot"));
var _ebnf = _interopRequireDefault(require("./ebnf"));
var _editorconfig = _interopRequireDefault(require("./editorconfig"));
var _eiffel = _interopRequireDefault(require("./eiffel"));
var _ejs = _interopRequireDefault(require("./ejs"));
var _elixir = _interopRequireDefault(require("./elixir"));
var _elm = _interopRequireDefault(require("./elm"));
var _erb = _interopRequireDefault(require("./erb"));
var _erlang = _interopRequireDefault(require("./erlang"));
var _etlua = _interopRequireDefault(require("./etlua"));
var _excelFormula = _interopRequireDefault(require("./excel-formula"));
var _factor = _interopRequireDefault(require("./factor"));
var _false = _interopRequireDefault(require("./false"));
var _firestoreSecurityRules = _interopRequireDefault(require("./firestore-security-rules"));
var _flow = _interopRequireDefault(require("./flow"));
var _fortran = _interopRequireDefault(require("./fortran"));
var _fsharp = _interopRequireDefault(require("./fsharp"));
var _ftl = _interopRequireDefault(require("./ftl"));
var _gap = _interopRequireDefault(require("./gap"));
var _gcode = _interopRequireDefault(require("./gcode"));
var _gdscript = _interopRequireDefault(require("./gdscript"));
var _gedcom = _interopRequireDefault(require("./gedcom"));
var _gherkin = _interopRequireDefault(require("./gherkin"));
var _git = _interopRequireDefault(require("./git"));
var _glsl = _interopRequireDefault(require("./glsl"));
var _gml = _interopRequireDefault(require("./gml"));
var _gn = _interopRequireDefault(require("./gn"));
var _goModule = _interopRequireDefault(require("./go-module"));
var _go = _interopRequireDefault(require("./go"));
var _graphql = _interopRequireDefault(require("./graphql"));
var _groovy = _interopRequireDefault(require("./groovy"));
var _haml = _interopRequireDefault(require("./haml"));
var _handlebars = _interopRequireDefault(require("./handlebars"));
var _haskell = _interopRequireDefault(require("./haskell"));
var _haxe = _interopRequireDefault(require("./haxe"));
var _hcl = _interopRequireDefault(require("./hcl"));
var _hlsl = _interopRequireDefault(require("./hlsl"));
var _hoon = _interopRequireDefault(require("./hoon"));
var _hpkp = _interopRequireDefault(require("./hpkp"));
var _hsts = _interopRequireDefault(require("./hsts"));
var _http = _interopRequireDefault(require("./http"));
var _ichigojam = _interopRequireDefault(require("./ichigojam"));
var _icon = _interopRequireDefault(require("./icon"));
var _icuMessageFormat = _interopRequireDefault(require("./icu-message-format"));
var _idris = _interopRequireDefault(require("./idris"));
var _iecst = _interopRequireDefault(require("./iecst"));
var _ignore = _interopRequireDefault(require("./ignore"));
var _inform = _interopRequireDefault(require("./inform7"));
var _ini = _interopRequireDefault(require("./ini"));
var _io = _interopRequireDefault(require("./io"));
var _j = _interopRequireDefault(require("./j"));
var _java = _interopRequireDefault(require("./java"));
var _javadoc = _interopRequireDefault(require("./javadoc"));
var _javadoclike = _interopRequireDefault(require("./javadoclike"));
var _javascript = _interopRequireDefault(require("./javascript"));
var _javastacktrace = _interopRequireDefault(require("./javastacktrace"));
var _jexl = _interopRequireDefault(require("./jexl"));
var _jolie = _interopRequireDefault(require("./jolie"));
var _jq = _interopRequireDefault(require("./jq"));
var _jsExtras = _interopRequireDefault(require("./js-extras"));
var _jsTemplates = _interopRequireDefault(require("./js-templates"));
var _jsdoc = _interopRequireDefault(require("./jsdoc"));
var _json = _interopRequireDefault(require("./json"));
var _json2 = _interopRequireDefault(require("./json5"));
var _jsonp = _interopRequireDefault(require("./jsonp"));
var _jsstacktrace = _interopRequireDefault(require("./jsstacktrace"));
var _jsx = _interopRequireDefault(require("./jsx"));
var _julia = _interopRequireDefault(require("./julia"));
var _keepalived = _interopRequireDefault(require("./keepalived"));
var _keyman = _interopRequireDefault(require("./keyman"));
var _kotlin = _interopRequireDefault(require("./kotlin"));
var _kumir = _interopRequireDefault(require("./kumir"));
var _kusto = _interopRequireDefault(require("./kusto"));
var _latex = _interopRequireDefault(require("./latex"));
var _latte = _interopRequireDefault(require("./latte"));
var _less = _interopRequireDefault(require("./less"));
var _lilypond = _interopRequireDefault(require("./lilypond"));
var _liquid = _interopRequireDefault(require("./liquid"));
var _lisp = _interopRequireDefault(require("./lisp"));
var _livescript = _interopRequireDefault(require("./livescript"));
var _llvm = _interopRequireDefault(require("./llvm"));
var _log = _interopRequireDefault(require("./log"));
var _lolcode = _interopRequireDefault(require("./lolcode"));
var _lua = _interopRequireDefault(require("./lua"));
var _magma = _interopRequireDefault(require("./magma"));
var _makefile = _interopRequireDefault(require("./makefile"));
var _markdown = _interopRequireDefault(require("./markdown"));
var _markupTemplating = _interopRequireDefault(require("./markup-templating"));
var _markup = _interopRequireDefault(require("./markup"));
var _matlab = _interopRequireDefault(require("./matlab"));
var _maxscript = _interopRequireDefault(require("./maxscript"));
var _mel = _interopRequireDefault(require("./mel"));
var _mermaid = _interopRequireDefault(require("./mermaid"));
var _mizar = _interopRequireDefault(require("./mizar"));
var _mongodb = _interopRequireDefault(require("./mongodb"));
var _monkey = _interopRequireDefault(require("./monkey"));
var _moonscript = _interopRequireDefault(require("./moonscript"));
var _n1ql = _interopRequireDefault(require("./n1ql"));
var _n4js = _interopRequireDefault(require("./n4js"));
var _nand2tetrisHdl = _interopRequireDefault(require("./nand2tetris-hdl"));
var _naniscript = _interopRequireDefault(require("./naniscript"));
var _nasm = _interopRequireDefault(require("./nasm"));
var _neon = _interopRequireDefault(require("./neon"));
var _nevod = _interopRequireDefault(require("./nevod"));
var _nginx = _interopRequireDefault(require("./nginx"));
var _nim = _interopRequireDefault(require("./nim"));
var _nix = _interopRequireDefault(require("./nix"));
var _nsis = _interopRequireDefault(require("./nsis"));
var _objectivec = _interopRequireDefault(require("./objectivec"));
var _ocaml = _interopRequireDefault(require("./ocaml"));
var _opencl = _interopRequireDefault(require("./opencl"));
var _openqasm = _interopRequireDefault(require("./openqasm"));
var _oz = _interopRequireDefault(require("./oz"));
var _parigp = _interopRequireDefault(require("./parigp"));
var _parser = _interopRequireDefault(require("./parser"));
var _pascal = _interopRequireDefault(require("./pascal"));
var _pascaligo = _interopRequireDefault(require("./pascaligo"));
var _pcaxis = _interopRequireDefault(require("./pcaxis"));
var _peoplecode = _interopRequireDefault(require("./peoplecode"));
var _perl = _interopRequireDefault(require("./perl"));
var _phpExtras = _interopRequireDefault(require("./php-extras"));
var _php = _interopRequireDefault(require("./php"));
var _phpdoc = _interopRequireDefault(require("./phpdoc"));
var _plsql = _interopRequireDefault(require("./plsql"));
var _powerquery = _interopRequireDefault(require("./powerquery"));
var _powershell = _interopRequireDefault(require("./powershell"));
var _processing = _interopRequireDefault(require("./processing"));
var _prolog = _interopRequireDefault(require("./prolog"));
var _promql = _interopRequireDefault(require("./promql"));
var _properties = _interopRequireDefault(require("./properties"));
var _protobuf = _interopRequireDefault(require("./protobuf"));
var _psl = _interopRequireDefault(require("./psl"));
var _pug = _interopRequireDefault(require("./pug"));
var _puppet = _interopRequireDefault(require("./puppet"));
var _pure = _interopRequireDefault(require("./pure"));
var _purebasic = _interopRequireDefault(require("./purebasic"));
var _purescript = _interopRequireDefault(require("./purescript"));
var _python = _interopRequireDefault(require("./python"));
var _q = _interopRequireDefault(require("./q"));
var _qml = _interopRequireDefault(require("./qml"));
var _qore = _interopRequireDefault(require("./qore"));
var _qsharp = _interopRequireDefault(require("./qsharp"));
var _r = _interopRequireDefault(require("./r"));
var _racket = _interopRequireDefault(require("./racket"));
var _reason = _interopRequireDefault(require("./reason"));
var _regex = _interopRequireDefault(require("./regex"));
var _rego = _interopRequireDefault(require("./rego"));
var _renpy = _interopRequireDefault(require("./renpy"));
var _rest = _interopRequireDefault(require("./rest"));
var _rip = _interopRequireDefault(require("./rip"));
var _roboconf = _interopRequireDefault(require("./roboconf"));
var _robotframework = _interopRequireDefault(require("./robotframework"));
var _ruby = _interopRequireDefault(require("./ruby"));
var _rust = _interopRequireDefault(require("./rust"));
var _sas = _interopRequireDefault(require("./sas"));
var _sass = _interopRequireDefault(require("./sass"));
var _scala = _interopRequireDefault(require("./scala"));
var _scheme = _interopRequireDefault(require("./scheme"));
var _scss = _interopRequireDefault(require("./scss"));
var _shellSession = _interopRequireDefault(require("./shell-session"));
var _smali = _interopRequireDefault(require("./smali"));
var _smalltalk = _interopRequireDefault(require("./smalltalk"));
var _smarty = _interopRequireDefault(require("./smarty"));
var _sml = _interopRequireDefault(require("./sml"));
var _solidity = _interopRequireDefault(require("./solidity"));
var _solutionFile = _interopRequireDefault(require("./solution-file"));
var _soy = _interopRequireDefault(require("./soy"));
var _sparql = _interopRequireDefault(require("./sparql"));
var _splunkSpl = _interopRequireDefault(require("./splunk-spl"));
var _sqf = _interopRequireDefault(require("./sqf"));
var _sql = _interopRequireDefault(require("./sql"));
var _squirrel = _interopRequireDefault(require("./squirrel"));
var _stan = _interopRequireDefault(require("./stan"));
var _stylus = _interopRequireDefault(require("./stylus"));
var _swift = _interopRequireDefault(require("./swift"));
var _systemd = _interopRequireDefault(require("./systemd"));
var _t4Cs = _interopRequireDefault(require("./t4-cs"));
var _t4Templating = _interopRequireDefault(require("./t4-templating"));
var _t4Vb = _interopRequireDefault(require("./t4-vb"));
var _tap = _interopRequireDefault(require("./tap"));
var _tcl = _interopRequireDefault(require("./tcl"));
var _textile = _interopRequireDefault(require("./textile"));
var _toml = _interopRequireDefault(require("./toml"));
var _tremor = _interopRequireDefault(require("./tremor"));
var _tsx = _interopRequireDefault(require("./tsx"));
var _tt = _interopRequireDefault(require("./tt2"));
var _turtle = _interopRequireDefault(require("./turtle"));
var _twig = _interopRequireDefault(require("./twig"));
var _typescript = _interopRequireDefault(require("./typescript"));
var _typoscript = _interopRequireDefault(require("./typoscript"));
var _unrealscript = _interopRequireDefault(require("./unrealscript"));
var _uorazor = _interopRequireDefault(require("./uorazor"));
var _uri = _interopRequireDefault(require("./uri"));
var _v = _interopRequireDefault(require("./v"));
var _vala = _interopRequireDefault(require("./vala"));
var _vbnet = _interopRequireDefault(require("./vbnet"));
var _velocity = _interopRequireDefault(require("./velocity"));
var _verilog = _interopRequireDefault(require("./verilog"));
var _vhdl = _interopRequireDefault(require("./vhdl"));
var _vim = _interopRequireDefault(require("./vim"));
var _visualBasic = _interopRequireDefault(require("./visual-basic"));
var _warpscript = _interopRequireDefault(require("./warpscript"));
var _wasm = _interopRequireDefault(require("./wasm"));
var _webIdl = _interopRequireDefault(require("./web-idl"));
var _wiki = _interopRequireDefault(require("./wiki"));
var _wolfram = _interopRequireDefault(require("./wolfram"));
var _wren = _interopRequireDefault(require("./wren"));
var _xeora = _interopRequireDefault(require("./xeora"));
var _xmlDoc = _interopRequireDefault(require("./xml-doc"));
var _xojo = _interopRequireDefault(require("./xojo"));
var _xquery = _interopRequireDefault(require("./xquery"));
var _yaml = _interopRequireDefault(require("./yaml"));
var _yang = _interopRequireDefault(require("./yang"));
var _zig = _interopRequireDefault(require("./zig"));