package com.superblog.config;

import com.google.inject.AbstractModule;
import com.google.inject.Provides;
import com.google.inject.Singleton;
import io.vertx.core.Vertx;
import io.vertx.core.json.JsonObject;

/**
 * 配置模块
 * 负责提供应用程序配置相关的依赖注入
 */
public class ConfigModule extends AbstractModule {

    private final Vertx vertx;
    private final JsonObject config;

    public ConfigModule(Vertx vertx, JsonObject config) {
        this.vertx = vertx;
        this.config = config;
    }

    @Override
    protected void configure() {
        // 绑定基础组件
        bind(Vertx.class).toInstance(vertx);
    }

    /**
     * 提供应用程序配置
     */
    @Provides
    @Singleton
    public AppConfig provideAppConfig() {
        return new AppConfig(config);
    }

    /**
     * 提供数据库配置
     */
    @Provides
    @Singleton
    public DatabaseConfig provideDatabaseConfig(AppConfig appConfig) {
        return appConfig.getDatabaseConfig();
    }

    /**
     * 提供Redis配置
     */
    @Provides
    @Singleton
    public RedisConfig provideRedisConfig(AppConfig appConfig) {
        return appConfig.getRedisConfig();
    }

    /**
     * 提供AI配置
     */
    @Provides
    @Singleton
    public AiConfig provideAiConfig(AppConfig appConfig) {
        return appConfig.getAiConfig();
    }

    /**
     * 提供文件配置
     */
    @Provides
    @Singleton
    public FileConfig provideFileConfig(AppConfig appConfig) {
        return appConfig.getFileConfig();
    }

    /**
     * 提供CORS配置
     */
    @Provides
    @Singleton
    public CorsConfig provideCorsConfig(AppConfig appConfig) {
        return appConfig.getCorsConfig();
    }
}
