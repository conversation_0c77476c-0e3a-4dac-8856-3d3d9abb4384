/**
 * 头部导航组件
 * 包含Logo、搜索框、主题切换和用户菜单
 */

import React from 'react';
import { Link } from 'react-router-dom';
import { 
  Menu, 
  Search, 
  Sun, 
  Moon, 
  Monitor,
  Bell,
  Settings,
  User
} from 'lucide-react';
import { Button, IconButton } from '@/components/ui/Button';
import { SearchInput } from '@/components/ui/Input';
import { useAppStore, useTheme } from '@/stores/appStore';

const Header: React.FC = () => {
  const { toggleSidebar, toggleMobileMenu } = useAppStore();
  const { theme, setTheme } = useTheme();

  const handleThemeChange = () => {
    const themes = ['light', 'dark', 'system'] as const;
    const currentIndex = themes.indexOf(theme);
    const nextTheme = themes[(currentIndex + 1) % themes.length];
    setTheme(nextTheme);
  };

  const getThemeIcon = () => {
    switch (theme) {
      case 'light':
        return <Sun className="w-5 h-5" />;
      case 'dark':
        return <Moon className="w-5 h-5" />;
      case 'system':
        return <Monitor className="w-5 h-5" />;
      default:
        return <Sun className="w-5 h-5" />;
    }
  };

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-white/80 dark:bg-gray-800/80 backdrop-blur-md border-b border-gray-200 dark:border-gray-700">
      <div className="flex items-center justify-between h-16 px-4">
        {/* Left Section */}
        <div className="flex items-center space-x-4">
          {/* Mobile Menu Button */}
          <IconButton
            variant="ghost"
            size="md"
            onClick={toggleMobileMenu}
            className="lg:hidden"
            aria-label="打开移动菜单"
            icon={<Menu />}
          />
          
          {/* Desktop Sidebar Toggle */}
          <IconButton
            variant="ghost"
            size="md"
            onClick={toggleSidebar}
            className="hidden lg:flex"
            aria-label="切换侧边栏"
            icon={<Menu />}
          />
          
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">SB</span>
            </div>
            <span className="hidden sm:block text-xl font-bold text-gray-900 dark:text-white">
              SuperBlog
            </span>
          </Link>
        </div>

        {/* Center Section - Search */}
        <div className="flex-1 max-w-md mx-4">
          <SearchInput
            placeholder="搜索文章、项目..."
            className="w-full"
          />
        </div>

        {/* Right Section */}
        <div className="flex items-center space-x-2">
          {/* Theme Toggle */}
          <IconButton
            variant="ghost"
            size="md"
            onClick={handleThemeChange}
            aria-label={`当前主题: ${theme}, 点击切换`}
            icon={getThemeIcon()}
          />
          
          {/* Notifications */}
          <IconButton
            variant="ghost"
            size="md"
            aria-label="通知"
            icon={<Bell />}
          />
          
          {/* Settings */}
          <IconButton
            variant="ghost"
            size="md"
            aria-label="设置"
            icon={<Settings />}
          />
          
          {/* User Menu */}
          <IconButton
            variant="ghost"
            size="md"
            aria-label="用户菜单"
            icon={<User />}
          />
        </div>
      </div>
    </header>
  );
};

export default Header;
