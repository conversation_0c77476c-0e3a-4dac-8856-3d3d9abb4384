export default {
    "code[class*=\"language-\"]": {
        "color": "white",
        "background": "none",
        "fontFamily": "Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace",
        "fontSize": "1em",
        "textAlign": "left",
        "textShadow": "0 -.1em .2em black",
        "whiteSpace": "pre",
        "wordSpacing": "normal",
        "wordBreak": "normal",
        "wordWrap": "normal",
        "lineHeight": "1.5",
        "MozTabSize": "4",
        "OTabSize": "4",
        "tabSize": "4",
        "WebkitHyphens": "none",
        "MozHyphens": "none",
        "msHyphens": "none",
        "hyphens": "none"
    },
    "pre[class*=\"language-\"]": {
        "color": "white",
        "background": "hsl(0, 0%, 8%)",
        "fontFamily": "Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace",
        "fontSize": "1em",
        "textAlign": "left",
        "textShadow": "0 -.1em .2em black",
        "whiteSpace": "pre",
        "wordSpacing": "normal",
        "wordBreak": "normal",
        "wordWrap": "normal",
        "lineHeight": "1.5",
        "MozTabSize": "4",
        "OTabSize": "4",
        "tabSize": "4",
        "WebkitHyphens": "none",
        "MozHyphens": "none",
        "msHyphens": "none",
        "hyphens": "none",
        "borderRadius": ".5em",
        "border": ".3em solid hsl(0, 0%, 33%)",
        "boxShadow": "1px 1px .5em black inset",
        "margin": ".5em 0",
        "overflow": "auto",
        "padding": "1em"
    },
    ":not(pre) > code[class*=\"language-\"]": {
        "background": "hsl(0, 0%, 8%)",
        "borderRadius": ".3em",
        "border": ".13em solid hsl(0, 0%, 33%)",
        "boxShadow": "1px 1px .3em -.1em black inset",
        "padding": ".15em .2em .05em",
        "whiteSpace": "normal"
    },
    "pre[class*=\"language-\"]::-moz-selection": {
        "background": "hsla(0, 0%, 93%, 0.15)",
        "textShadow": "none"
    },
    "pre[class*=\"language-\"]::selection": {
        "background": "hsla(0, 0%, 93%, 0.15)",
        "textShadow": "none"
    },
    "pre[class*=\"language-\"] ::-moz-selection": {
        "textShadow": "none",
        "background": "hsla(0, 0%, 93%, 0.15)"
    },
    "code[class*=\"language-\"]::-moz-selection": {
        "textShadow": "none",
        "background": "hsla(0, 0%, 93%, 0.15)"
    },
    "code[class*=\"language-\"] ::-moz-selection": {
        "textShadow": "none",
        "background": "hsla(0, 0%, 93%, 0.15)"
    },
    "pre[class*=\"language-\"] ::selection": {
        "textShadow": "none",
        "background": "hsla(0, 0%, 93%, 0.15)"
    },
    "code[class*=\"language-\"]::selection": {
        "textShadow": "none",
        "background": "hsla(0, 0%, 93%, 0.15)"
    },
    "code[class*=\"language-\"] ::selection": {
        "textShadow": "none",
        "background": "hsla(0, 0%, 93%, 0.15)"
    },
    "comment": {
        "color": "hsl(0, 0%, 47%)"
    },
    "prolog": {
        "color": "hsl(0, 0%, 47%)"
    },
    "doctype": {
        "color": "hsl(0, 0%, 47%)"
    },
    "cdata": {
        "color": "hsl(0, 0%, 47%)"
    },
    "punctuation": {
        "Opacity": ".7"
    },
    "namespace": {
        "Opacity": ".7"
    },
    "tag": {
        "color": "hsl(14, 58%, 55%)"
    },
    "boolean": {
        "color": "hsl(14, 58%, 55%)"
    },
    "number": {
        "color": "hsl(14, 58%, 55%)"
    },
    "deleted": {
        "color": "hsl(14, 58%, 55%)"
    },
    "keyword": {
        "color": "hsl(53, 89%, 79%)"
    },
    "property": {
        "color": "hsl(53, 89%, 79%)"
    },
    "selector": {
        "color": "hsl(53, 89%, 79%)"
    },
    "constant": {
        "color": "hsl(53, 89%, 79%)"
    },
    "symbol": {
        "color": "hsl(53, 89%, 79%)"
    },
    "builtin": {
        "color": "hsl(53, 89%, 79%)"
    },
    "attr-name": {
        "color": "hsl(76, 21%, 52%)"
    },
    "attr-value": {
        "color": "hsl(76, 21%, 52%)"
    },
    "string": {
        "color": "hsl(76, 21%, 52%)"
    },
    "char": {
        "color": "hsl(76, 21%, 52%)"
    },
    "operator": {
        "color": "hsl(76, 21%, 52%)"
    },
    "entity": {
        "color": "hsl(76, 21%, 52%)",
        "cursor": "help"
    },
    "url": {
        "color": "hsl(76, 21%, 52%)"
    },
    ".language-css .token.string": {
        "color": "hsl(76, 21%, 52%)"
    },
    ".style .token.string": {
        "color": "hsl(76, 21%, 52%)"
    },
    "variable": {
        "color": "hsl(76, 21%, 52%)"
    },
    "inserted": {
        "color": "hsl(76, 21%, 52%)"
    },
    "atrule": {
        "color": "hsl(218, 22%, 55%)"
    },
    "regex": {
        "color": "hsl(42, 75%, 65%)"
    },
    "important": {
        "color": "hsl(42, 75%, 65%)",
        "fontWeight": "bold"
    },
    "bold": {
        "fontWeight": "bold"
    },
    "italic": {
        "fontStyle": "italic"
    },
    ".language-markup .token.tag": {
        "color": "hsl(33, 33%, 52%)"
    },
    ".language-markup .token.attr-name": {
        "color": "hsl(33, 33%, 52%)"
    },
    ".language-markup .token.punctuation": {
        "color": "hsl(33, 33%, 52%)"
    },
    "": {
        "position": "relative",
        "zIndex": "1"
    },
    ".line-highlight.line-highlight": {
        "background": "linear-gradient(to right, hsla(0, 0%, 33%, .1) 70%, hsla(0, 0%, 33%, 0))",
        "borderBottom": "1px dashed hsl(0, 0%, 33%)",
        "borderTop": "1px dashed hsl(0, 0%, 33%)",
        "marginTop": "0.75em",
        "zIndex": "0"
    },
    ".line-highlight.line-highlight:before": {
        "backgroundColor": "hsl(215, 15%, 59%)",
        "color": "hsl(24, 20%, 95%)"
    },
    ".line-highlight.line-highlight[data-end]:after": {
        "backgroundColor": "hsl(215, 15%, 59%)",
        "color": "hsl(24, 20%, 95%)"
    }
}