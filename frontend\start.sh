#!/bin/bash

# SuperBlog Frontend 启动脚本

echo "🚀 启动 SuperBlog Frontend..."
echo "================================"

# 检查Node.js版本
echo "📋 检查环境..."
node_version=$(node -v)
npm_version=$(npm -v)
echo "Node.js版本: $node_version"
echo "npm版本: $npm_version"

# 检查依赖是否已安装
if [ ! -d "node_modules" ]; then
    echo "📦 安装依赖..."
    npm install
fi

# 启动开发服务器
echo "🌐 启动开发服务器..."
echo "访问地址: http://localhost:3000"
echo "如果3000端口被占用，Vite会自动选择其他端口"
echo "================================"

npm run dev
