package com.superblog;

import com.superblog.infrastructure.web.SimpleRouterFactory;
import io.vertx.core.Vertx;
import io.vertx.core.http.HttpServer;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * SuperBlog 应用程序启动类
 * 提供标准的main方法入口，方便在IDE中运行
 */
public class Application {

    private static final Logger logger = LoggerFactory.getLogger(Application.class);

    public static void main(String[] args) {
        // 设置日志代理
        System.setProperty("vertx.logger-delegate-factory-class-name", 
            "io.vertx.core.logging.SLF4JLogDelegateFactory");

        logger.info("🚀 正在启动 SuperBlog 应用程序...");

        // 创建Vertx实例
        Vertx vertx = Vertx.vertx();

        // 加载配置
        JsonObject config = loadConfiguration();
        
        // 打印启动信息
        printStartupInfo(config);

        // 部署SimpleRouterFactory版本（避免复杂依赖）
        deploySimpleVersion(vertx, config);

        // 添加关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            logger.info("🛑 正在关闭 SuperBlog 应用程序...");
            vertx.close().onComplete(result -> {
                if (result.succeeded()) {
                    logger.info("✅ SuperBlog 应用程序已安全关闭");
                } else {
                    logger.error("❌ 关闭应用程序时发生错误", result.cause());
                }
            });
        }));
    }

    /**
     * 部署简化版本（避免复杂的依赖注入）
     */
    private static void deploySimpleVersion(Vertx vertx, JsonObject config) {
        try {
            // 创建HTTP服务器
            HttpServer server = vertx.createHttpServer();

            // 使用SimpleRouterFactory创建路由
            SimpleRouterFactory routerFactory = new SimpleRouterFactory(vertx);
            Router router = routerFactory.createRouter();

            // 启动服务器
            int port = config.getInteger("server.port", 8080);
            String host = config.getString("server.host", "0.0.0.0");

            server.requestHandler(router)
                .listen(port, host)
                .onSuccess(httpServer -> {
                    logger.info("✅ SuperBlog 启动成功！");
                    logger.info("🌐 访问地址: http://localhost:{}", port);
                    logger.info("❤️ 健康检查: http://localhost:{}/api/health", port);
                    logger.info("🤖 AI接口测试: POST http://localhost:{}/api/ai/text-to-image", port);
                    logger.info("💬 通义千问测试: POST http://localhost:{}/api/ai/chat", port);
                })
                .onFailure(throwable -> {
                    logger.error("❌ SuperBlog 启动失败", throwable);
                    System.exit(1);
                });

        } catch (Exception e) {
            logger.error("❌ 部署简化版本失败", e);
            System.exit(1);
        }
    }

    /**
     * 加载配置文件
     */
    private static JsonObject loadConfiguration() {
        try {
            // 确定配置文件名
            String profile = getProfile();
            String configFile = profile != null ? 
                "application-" + profile + ".json" : "application.json";
            
            logger.info("📋 加载配置文件: {}", configFile);

            // 从classpath读取配置
            var inputStream = Application.class.getClassLoader()
                .getResourceAsStream(configFile);

            JsonObject config;
            if (inputStream != null) {
                String configContent = new String(inputStream.readAllBytes());
                config = new JsonObject(configContent);
                logger.info("✅ 配置文件加载成功");
            } else {
                logger.warn("⚠️ 配置文件 {} 不存在，使用默认配置", configFile);
                config = getDefaultConfig();
            }

            // 替换环境变量
            replaceEnvironmentVariables(config);
            
            return config;
        } catch (Exception e) {
            logger.error("❌ 加载配置文件失败，使用默认配置", e);
            return getDefaultConfig();
        }
    }

    /**
     * 获取运行环境
     */
    private static String getProfile() {
        // 优先使用系统属性
        String profile = System.getProperty("profile");
        if (profile == null) {
            // 其次使用环境变量
            profile = System.getenv("PROFILE");
        }
        if (profile == null) {
            // 默认为dev环境
            profile = "dev";
        }
        return profile;
    }

    /**
     * 获取默认配置
     */
    private static JsonObject getDefaultConfig() {
        return new JsonObject()
            .put("server", new JsonObject()
                .put("port", 8080)
                .put("host", "0.0.0.0"))
            .put("database", new JsonObject()
                .put("host", "localhost")
                .put("port", 5432)
                .put("database", "superblog_dev")
                .put("username", "superblog")
                .put("password", "superblog123")
                .put("maxPoolSize", 10)
                .put("connectTimeout", 5000)
                .put("idleTimeout", 300000))
            .put("redis", new JsonObject()
                .put("host", "localhost")
                .put("port", 6379)
                .put("database", 1)
                .put("maxPoolSize", 5)
                .put("maxWaitingHandlers", 50))
            .put("ai", new JsonObject()
                .put("openai", new JsonObject()
                    .put("apiKey", System.getenv("OPENAI_API_KEY"))
                    .put("baseUrl", "https://api.openai.com/v1")
                    .put("model", "dall-e-3")
                    .put("timeout", 60000))
                .put("stabilityai", new JsonObject()
                    .put("apiKey", System.getenv("STABILITY_API_KEY"))
                    .put("baseUrl", "https://api.stability.ai/v1")
                    .put("timeout", 60000))
                .put("qwen", new JsonObject()
                    .put("apiKey", "sk-4606dfde828a4f9aa7a43f5d53dddb9e")
                    .put("baseUrl", "https://dashscope.aliyuncs.com/api/v1")
                    .put("model", "qwen-turbo")
                    .put("timeout", 60000)))
            .put("file", new JsonObject()
                .put("uploadPath", "./uploads")
                .put("maxFileSize", 10485760)
                .put("allowedTypes", new io.vertx.core.json.JsonArray()
                    .add("image/jpeg").add("image/png").add("image/gif").add("image/webp")))
            .put("cors", new JsonObject()
                .put("allowedOrigins", new io.vertx.core.json.JsonArray().add("*"))
                .put("allowedMethods", new io.vertx.core.json.JsonArray()
                    .add("GET").add("POST").add("PUT").add("DELETE").add("OPTIONS"))
                .put("allowedHeaders", new io.vertx.core.json.JsonArray()
                    .add("Content-Type").add("Authorization").add("X-Requested-With"))
                .put("allowCredentials", true))
            .put("logging", new JsonObject()
                .put("level", "DEBUG"));
    }

    /**
     * 替换配置中的环境变量
     */
    private static void replaceEnvironmentVariables(JsonObject config) {
        replaceEnvInJsonObject(config);
    }

    private static void replaceEnvInJsonObject(JsonObject obj) {
        for (String key : obj.fieldNames()) {
            Object value = obj.getValue(key);
            if (value instanceof String) {
                String strValue = (String) value;
                if (strValue != null && strValue.startsWith("${") && strValue.endsWith("}")) {
                    String envVar = strValue.substring(2, strValue.length() - 1);
                    String envValue = System.getenv(envVar);
                    if (envValue != null) {
                        obj.put(key, envValue);
                        logger.debug("🔧 替换环境变量 {} = {}", envVar, "***");
                    } else {
                        logger.warn("⚠️ 环境变量 {} 未设置", envVar);
                    }
                }
            } else if (value instanceof JsonObject) {
                replaceEnvInJsonObject((JsonObject) value);
            }
        }
    }

    /**
     * 打印启动信息
     */
    private static void printStartupInfo(JsonObject config) {
        logger.info("=".repeat(60));
        logger.info("🎯 SuperBlog 个人技术展示平台");
        logger.info("📋 运行环境: {}", getProfile());
        logger.info("🌐 服务端口: {}", config.getInteger("server.port", 8080));
        logger.info("🗄️ 数据库: {}:{}/{}", 
            config.getJsonObject("database").getString("host"),
            config.getJsonObject("database").getInteger("port"),
            config.getJsonObject("database").getString("database"));
        logger.info("📦 Redis: {}:{}/{}", 
            config.getJsonObject("redis").getString("host"),
            config.getJsonObject("redis").getInteger("port"),
            config.getJsonObject("redis").getInteger("database"));
        
        // 检查API密钥
        JsonObject aiConfig = config.getJsonObject("ai");
        if (aiConfig != null) {
            String openaiKey = aiConfig.getJsonObject("openai").getString("apiKey");
            String stabilityKey = aiConfig.getJsonObject("stabilityai").getString("apiKey");
            String qwenKey = aiConfig.getJsonObject("qwen").getString("apiKey");
            logger.info("🤖 OpenAI API: {}", openaiKey != null ? "已配置" : "未配置");
            logger.info("🎨 Stability AI: {}", stabilityKey != null ? "已配置" : "未配置");
            logger.info("💬 通义千问 API: {}", qwenKey != null ? "已配置" : "未配置");
        }
        
        logger.info("=".repeat(60));
    }
}
