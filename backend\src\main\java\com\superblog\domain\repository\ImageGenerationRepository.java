package com.superblog.domain.repository;

import com.superblog.domain.model.ImageGeneration;
import io.vertx.core.Future;
import java.util.List;
import java.util.Optional;

/**
 * AI图片生成仓储接口
 * 定义图片生成相关的数据访问操作
 */
public interface ImageGenerationRepository {

    /**
     * 保存图片生成记录
     */
    Future<ImageGeneration> save(ImageGeneration imageGeneration);

    /**
     * 根据ID查找图片生成记录
     */
    Future<Optional<ImageGeneration>> findById(Long id);

    /**
     * 根据用户ID查找图片生成记录列表
     */
    Future<List<ImageGeneration>> findByUserId(Long userId, int page, int size);

    /**
     * 查找公开的图片生成记录列表
     */
    Future<List<ImageGeneration>> findPublicGenerations(int page, int size);

    /**
     * 根据生成类型查找图片生成记录列表
     */
    Future<List<ImageGeneration>> findByGenerationType(ImageGeneration.GenerationType type, int page, int size);

    /**
     * 根据状态查找图片生成记录列表
     */
    Future<List<ImageGeneration>> findByStatus(ImageGeneration.GenerationStatus status, int page, int size);

    /**
     * 更新图片生成记录
     */
    Future<ImageGeneration> update(ImageGeneration imageGeneration);

    /**
     * 根据ID删除图片生成记录
     */
    Future<Boolean> deleteById(Long id);

    /**
     * 统计用户的图片生成总数
     */
    Future<Long> countByUserId(Long userId);

    /**
     * 统计公开的图片生成总数
     */
    Future<Long> countPublicGenerations();

    /**
     * 增加查看次数
     */
    Future<Void> incrementViewCount(Long id);

    /**
     * 增加点赞次数
     */
    Future<Void> incrementLikeCount(Long id);

    /**
     * 获取热门图片生成记录
     */
    Future<List<ImageGeneration>> findPopularGenerations(int limit);

    /**
     * 获取最新图片生成记录
     */
    Future<List<ImageGeneration>> findLatestGenerations(int limit);

    /**
     * 根据关键词搜索图片生成记录
     */
    Future<List<ImageGeneration>> searchByKeyword(String keyword, int page, int size);
}
