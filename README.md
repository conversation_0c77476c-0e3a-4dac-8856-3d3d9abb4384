# SuperBlog 技术展示网站

## 项目架构

### 前端技术栈
- React 18
- TypeScript
- Ant Design 5.0
- Redux Toolkit
- React Router 6

### 后端技术栈
- Java 21
- Spring Boot 3
- Spring Security
- Spring Data JPA
- PostgreSQL

### 基础设施
- Docker
- Nginx
- Redis

## 功能模块

### 1. 博客系统
- Markdown 编辑器
- 文章分类和标签
- 评论系统
- 文章搜索

### 2. 视频作品展示
- 视频上传和播放
- 视频分类管理
- 视频封面管理
- 播放统计

### 3. AI 能力展示
- 文生图功能（集成 Stable Diffusion API）
- 智能对话（集成 ChatGPT API）
- 图片编辑和风格转换
- AI 模型能力展示

### 4. 通用功能
- 用户认证和授权
- 个人资料管理
- 系统管理后台
- 数据统计和分析

## 项目结构
```
superblog/
├── frontend/           # React 前端项目
├── backend/            # Spring Boot 后端项目
├── docs/              # 项目文档
└── docker/            # Docker 配置文件
```

## 开发计划

### 第一阶段：基础架构搭建
1. 搭建前端开发环境
2. 搭建后端开发环境
3. 实现用户认证系统
4. 搭建基础 UI 框架

### 第二阶段：博客系统
1. 实现文章 CRUD
2. 实现评论功能
3. 实现文章搜索

### 第三阶段：视频系统
1. 实现视频上传
2. 实现视频播放
3. 实现视频管理

### 第四阶段：AI 功能
1. 集成 AI API
2. 实现文生图功能
3. 实现智能对话

## 部署要求
- JDK 21+
- Node.js 18+
- PostgreSQL 15+
- Redis 7+
- Docker 24+

## 开发规范
1. 代码规范遵循各语言社区最佳实践
2. 使用 Git Flow 工作流
3. 提交信息遵循 Conventional Commits
4. 所有功能需要编写单元测试