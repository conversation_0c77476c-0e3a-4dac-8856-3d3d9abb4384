# SuperBlog - 个人技术展示平台

一个现代化的个人技术展示平台，集成AI图片生成、视频展示、博客、项目展示等功能。

## 🎯 项目概述

SuperBlog是一个全栈的个人技术展示平台，旨在为技术人员提供一个展示自己技术能力、分享知识和作品的综合性平台。

### 核心特色

- 🤖 **AI图片生成工具** - 集成OpenAI DALL-E和Stability AI，支持文生图和图生图
- 🎥 **视频展示模块** - 支持本地视频和外部平台链接
- 📝 **技术博客系统** - Markdown编辑，分类标签，全文搜索
- 💼 **项目展示** - GitHub项目同步，技术栈展示
- 🛠️ **实用小工具** - JSON格式化、二维码生成等开发工具

## 📁 项目结构

```
SuperBlog/
├── backend/                 # 后端服务 (Java 21 + Vert.x)
│   ├── src/main/java/       # Java源代码
│   ├── src/main/resources/  # 配置文件和数据库迁移
│   ├── dev-ops/            # Docker部署配置
│   ├── api-tests.sh        # API测试脚本
│   ├── start.sh            # 启动脚本
│   └── README.md           # 后端详细说明
├── frontend/               # 前端应用 (待开发)
├── prototype/              # HTML原型设计
│   └── index.html          # 完整的UI原型
└── README.md               # 项目总览
```

## 🚀 快速开始

### 1. 查看原型设计

首先查看完整的UI设计原型：

```bash
# 在浏览器中打开原型文件
open prototype/index.html
```

原型包含了所有主要功能模块的界面设计，可以直观地了解项目的功能和布局。

### 2. 启动后端服务

```bash
# 进入后端目录
cd backend

# 给脚本执行权限
chmod +x start.sh api-tests.sh quick-test.sh

# 启动后端服务
./start.sh
```

### 3. 测试API接口

```bash
# 快速测试
./quick-test.sh

# 完整API测试
./api-tests.sh
```

## 🛠️ 技术栈

### 后端技术栈
- **Java 21** - 最新LTS版本，现代Java特性
- **Vert.x 4.x** - 高性能异步Web框架
- **PostgreSQL** - 主数据库
- **Redis** - 缓存和会话存储
- **Flyway** - 数据库迁移管理
- **Google Guice** - 依赖注入
- **Maven** - 构建工具

### 前端技术栈 (计划)
- **React 18** - 现代前端框架
- **Next.js** - 全栈React框架
- **TypeScript** - 类型安全
- **Tailwind CSS** - 原子化CSS框架
- **Zustand** - 状态管理

### 外部服务集成
- **OpenAI DALL-E** - AI图片生成
- **Stability AI** - AI图片生成
- **GitHub API** - 项目信息同步

## 🎨 功能模块

### 1. AI图片生成工具 (🔥 核心功能)

**文生图功能**
- 支持中文提示词
- 多种艺术风格选择
- 参数自定义 (尺寸、步数、CFG等)
- 生成历史管理

**图生图功能**
- 基于原图进行风格转换
- 支持多种转换模式
- 实时预览效果

**API接口**
```bash
# 创建文生图任务
POST /api/ai/text-to-image
{
  "prompt": "一只可爱的橘猫坐在樱花树下，动漫风格",
  "width": 512,
  "height": 512,
  "style": "动漫风格"
}

# 获取生成历史
GET /api/ai/generations/public?page=0&size=10
```

### 2. 视频展示模块

- 视频上传和管理
- 支持YouTube、Bilibili等外部链接
- 视频分类和标签
- 播放统计和评论系统

### 3. 技术博客系统

- Markdown文章编辑
- 代码语法高亮
- 分类和标签管理
- 全文搜索功能
- SEO优化

### 4. 项目展示模块

- GitHub项目自动同步
- 技术栈可视化展示
- 项目演示链接
- Star和Fork统计

### 5. 实用小工具

- JSON格式化和验证
- 二维码生成器
- 密码生成器
- 时间戳转换
- Base64编解码
- 颜色选择器

## 🏗️ 架构设计

### 后端架构 (DDD)

```
├── Domain Layer (领域层)
│   ├── Model - 领域模型
│   └── Repository - 仓储接口
├── Application Layer (应用层)
│   ├── Service - 应用服务
│   └── DTO - 数据传输对象
└── Infrastructure Layer (基础设施层)
    ├── Repository - 仓储实现
    ├── External - 外部服务
    └── Web - Web接口
```

### 数据库设计

主要数据表：
- `users` - 用户信息
- `image_generations` - AI图片生成记录
- `videos` - 视频信息
- `articles` - 文章内容
- `projects` - 项目信息
- `categories` - 分类管理
- `tags` - 标签管理

## 🐳 部署方案

### Docker部署

```bash
cd backend/dev-ops
docker-compose up -d
```

### 生产环境部署

1. **数据库准备**
```bash
# PostgreSQL
createdb superblog
createuser superblog

# Redis
redis-server
```

2. **应用部署**
```bash
# 构建应用
cd backend
mvn clean package

# 启动应用
java -jar target/superblog-backend-1.0.0-SNAPSHOT-fat.jar
```

3. **Nginx配置**
```nginx
location /api/ {
    proxy_pass http://localhost:8080;
}
```

## 📊 开发进度

### ✅ 已完成
- [x] 项目架构设计
- [x] 数据库设计和迁移
- [x] 后端基础框架搭建
- [x] AI图片生成核心功能
- [x] RESTful API设计
- [x] Docker部署配置
- [x] 完整的HTML原型设计
- [x] API测试脚本

### 🚧 进行中
- [ ] 完善AI图片生成功能
- [ ] 实现文件上传服务
- [ ] 添加用户认证系统

### 📋 待开发
- [ ] 前端React应用开发
- [ ] 视频模块完整实现
- [ ] 博客模块完整实现
- [ ] 项目模块完整实现
- [ ] 小工具模块实现
- [ ] 用户管理和权限系统
- [ ] 评论和互动功能
- [ ] 搜索和推荐系统
- [ ] 性能优化和缓存
- [ ] 监控和日志系统

## 🧪 测试

### API测试
```bash
# 快速测试
cd backend
./quick-test.sh

# 完整测试
./api-tests.sh
```

### 功能测试
- 健康检查: `GET /api/health`
- AI图片生成: `POST /api/ai/text-to-image`
- 获取生成历史: `GET /api/ai/generations/public`

## 📝 开发指南

### 代码规范
- 所有代码使用中文注释
- 遵循DDD架构模式
- RESTful API设计规范
- 统一的错误处理和响应格式

### 添加新功能
1. 在`domain/model`中定义领域模型
2. 在`domain/repository`中定义仓储接口
3. 在`infrastructure/repository`中实现仓储
4. 在`application/service`中实现业务逻辑
5. 在`infrastructure/web/controller`中实现API
6. 在`api-tests.sh`中添加测试用例

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交代码
4. 创建Pull Request

## 📄 许可证

MIT License

---

**SuperBlog** - 让技术分享更简单，让创作更有趣！
