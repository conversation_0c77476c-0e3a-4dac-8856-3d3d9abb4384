package com.superblog.infrastructure.web.util;

import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;

/**
 * API响应工具类
 * 提供统一的响应格式
 */
public class ApiResponse {

    /**
     * 成功响应
     */
    public static void success(RoutingContext context, String message, Object data) {
        JsonObject response = new JsonObject()
            .put("success", true)
            .put("code", 200)
            .put("message", message)
            .put("data", data)
            .put("timestamp", System.currentTimeMillis());

        context.response()
            .setStatusCode(200)
            .putHeader("Content-Type", "application/json")
            .end(response.encode());
    }

    /**
     * 成功响应（无数据）
     */
    public static void success(RoutingContext context, String message) {
        success(context, message, null);
    }

    /**
     * 错误响应
     */
    public static void error(RoutingContext context, String message, Object data) {
        JsonObject response = new JsonObject()
            .put("success", false)
            .put("code", 500)
            .put("message", message)
            .put("data", data)
            .put("timestamp", System.currentTimeMillis());

        context.response()
            .setStatusCode(500)
            .putHeader("Content-Type", "application/json")
            .end(response.encode());
    }

    /**
     * 错误响应（无数据）
     */
    public static void error(RoutingContext context, String message) {
        error(context, message, null);
    }

    /**
     * 客户端错误响应
     */
    public static void badRequest(RoutingContext context, String message) {
        JsonObject response = new JsonObject()
            .put("success", false)
            .put("code", 400)
            .put("message", message)
            .put("timestamp", System.currentTimeMillis());

        context.response()
            .setStatusCode(400)
            .putHeader("Content-Type", "application/json")
            .end(response.encode());
    }

    /**
     * 未找到响应
     */
    public static void notFound(RoutingContext context, String message) {
        JsonObject response = new JsonObject()
            .put("success", false)
            .put("code", 404)
            .put("message", message)
            .put("timestamp", System.currentTimeMillis());

        context.response()
            .setStatusCode(404)
            .putHeader("Content-Type", "application/json")
            .end(response.encode());
    }

    /**
     * 未授权响应
     */
    public static void unauthorized(RoutingContext context, String message) {
        JsonObject response = new JsonObject()
            .put("success", false)
            .put("code", 401)
            .put("message", message)
            .put("timestamp", System.currentTimeMillis());

        context.response()
            .setStatusCode(401)
            .putHeader("Content-Type", "application/json")
            .end(response.encode());
    }
}
