package com.superblog.config;

import com.google.inject.AbstractModule;
import com.google.inject.Provides;
import com.google.inject.Singleton;
import com.superblog.infrastructure.database.DatabaseMigration;
import com.superblog.infrastructure.database.DatabasePool;
import com.superblog.infrastructure.database.RedisPool;
import io.vertx.core.Vertx;
import io.vertx.pgclient.PgPool;
import io.vertx.redis.client.Redis;

/**
 * 数据库模块
 * 负责提供数据库相关的依赖注入
 */
public class DatabaseModule extends AbstractModule {

    @Override
    protected void configure() {
        // 绑定数据库相关组件
        bind(DatabaseMigration.class).in(Singleton.class);
    }

    /**
     * 提供PostgreSQL连接池
     */
    @Provides
    @Singleton
    public PgPool providePgPool(Vertx vertx, DatabaseConfig databaseConfig) {
        return DatabasePool.create(vertx, databaseConfig);
    }

    /**
     * 提供Redis客户端
     */
    @Provides
    @Singleton
    public Redis provideRedis(Vertx vertx, RedisConfig redisConfig) {
        return RedisPool.create(vertx, redisConfig);
    }
}
