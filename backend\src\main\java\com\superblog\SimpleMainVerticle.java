package com.superblog;

import com.superblog.infrastructure.web.SimpleRouterFactory;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.http.HttpServer;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;

/**
 * 简化的主Verticle
 * 避免复杂的依赖注入，确保能够启动
 */
public class SimpleMainVerticle extends AbstractVerticle {

    @Override
    public void start(Promise<Void> startPromise) {
        System.out.println("🚀 启动 SimpleMainVerticle...");

        try {
            // 获取配置
            JsonObject config = config();
            int port = config.getInteger("server.port", 8080);
            String host = config.getString("server.host", "0.0.0.0");

            // 创建HTTP服务器
            HttpServer server = vertx.createHttpServer();

            // 使用SimpleRouterFactory创建路由
            SimpleRouterFactory routerFactory = new SimpleRouterFactory(vertx);
            Router router = routerFactory.createRouter();

            // 启动服务器
            server.requestHandler(router)
                .listen(port, host)
                .onSuccess(httpServer -> {
                    System.out.println("✅ SimpleMainVerticle 启动成功！");
                    System.out.println("🌐 访问地址: http://localhost:" + port);
                    System.out.println("❤️ 健康检查: http://localhost:" + port + "/api/health");
                    System.out.println("🤖 AI接口测试: POST http://localhost:" + port + "/api/ai/text-to-image");
                    System.out.println("💬 通义千问测试: POST http://localhost:" + port + "/api/ai/chat");
                    startPromise.complete();
                })
                .onFailure(throwable -> {
                    System.err.println("❌ SimpleMainVerticle 启动失败: " + throwable.getMessage());
                    throwable.printStackTrace();
                    startPromise.fail(throwable);
                });

        } catch (Exception e) {
            System.err.println("❌ SimpleMainVerticle 启动过程中发生错误: " + e.getMessage());
            e.printStackTrace();
            startPromise.fail(e);
        }
    }

    @Override
    public void stop(Promise<Void> stopPromise) {
        System.out.println("🛑 正在停止 SimpleMainVerticle...");
        stopPromise.complete();
        System.out.println("✅ SimpleMainVerticle 已停止");
    }

    /**
     * 主入口方法（可选）
     */
    public static void main(String[] args) {
        System.out.println("🚀 启动 SimpleMainVerticle 独立模式...");

        // 创建Vertx实例
        io.vertx.core.Vertx vertx = io.vertx.core.Vertx.vertx();

        // 创建配置
        JsonObject config = new JsonObject()
            .put("server", new JsonObject()
                .put("port", 8080)
                .put("host", "0.0.0.0"));

        // 部署Verticle
        vertx.deployVerticle(new SimpleMainVerticle(),
            new io.vertx.core.DeploymentOptions().setConfig(config))
            .onSuccess(deploymentId -> {
                System.out.println("✅ SimpleMainVerticle 部署成功，ID: " + deploymentId);
            })
            .onFailure(throwable -> {
                System.err.println("❌ SimpleMainVerticle 部署失败: " + throwable.getMessage());
                throwable.printStackTrace();
                System.exit(1);
            });
    }
}
