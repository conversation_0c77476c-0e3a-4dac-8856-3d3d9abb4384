package com.superblog.infrastructure.web;

import io.vertx.core.Vertx;
import io.vertx.core.http.HttpMethod;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.handler.BodyHandler;
import io.vertx.ext.web.handler.CorsHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 简化的路由工厂类
 * 用于创建和配置Web路由，避免复杂的依赖注入问题
 */
public class SimpleRouterFactory {

    private static final Logger logger = LoggerFactory.getLogger(SimpleRouterFactory.class);

    private final Vertx vertx;

    public SimpleRouterFactory(Vertx vertx) {
        this.vertx = vertx;
    }

    /**
     * 创建路由器
     */
    public Router createRouter() {
        Router router = Router.router(vertx);

        // 配置中间件
        configureMiddleware(router);

        // 配置API路由
        configureApiRoutes(router);

        // 配置错误处理
        configureErrorHandling(router);

        logger.info("路由配置完成");
        return router;
    }

    /**
     * 配置中间件
     */
    private void configureMiddleware(Router router) {
        // CORS处理
        CorsHandler corsHandler = CorsHandler.create()
            .addOrigin("*")
            .allowedMethod(HttpMethod.GET)
            .allowedMethod(HttpMethod.POST)
            .allowedMethod(HttpMethod.PUT)
            .allowedMethod(HttpMethod.DELETE)
            .allowedMethod(HttpMethod.OPTIONS)
            .allowedHeader("Content-Type")
            .allowedHeader("Authorization")
            .allowedHeader("X-Requested-With")
            .allowCredentials(true);

        router.route().handler(corsHandler);

        // 请求体处理
        router.route().handler(BodyHandler.create()
            .setBodyLimit(10 * 1024 * 1024) // 10MB
            .setMergeFormAttributes(true)
            .setDeleteUploadedFilesOnEnd(true));

        // 请求日志
        router.route().handler(context -> {
            String method = context.request().method().toString();
            String path = context.request().path();
            logger.debug("收到请求: {} {}", method, path);
            context.next();
        });
    }

    /**
     * 配置API路由
     */
    private void configureApiRoutes(Router router) {
        // API基础路径
        Router apiRouter = Router.router(vertx);

        // 健康检查
        apiRouter.get("/health").handler(context -> {
            JsonObject response = new JsonObject()
                .put("status", "UP")
                .put("timestamp", System.currentTimeMillis())
                .put("service", "SuperBlog Backend")
                .put("version", "1.0.0");

            context.response()
                .putHeader("Content-Type", "application/json")
                .end(response.encode());
        });

        // AI图片生成相关路由
        configureImageGenerationRoutes(apiRouter);

        // 通义千问AI路由
        configureQwenRoutes(apiRouter);

        // 其他模块路由
        configureOtherRoutes(apiRouter);

        // 挂载API路由
        router.mountSubRouter("/api", apiRouter);
    }

    /**
     * 配置AI图片生成路由
     */
    private void configureImageGenerationRoutes(Router router) {
        // 创建文生图任务
        router.post("/ai/text-to-image").handler(context -> {
            JsonObject requestBody = context.body().asJsonObject();
            String prompt = requestBody.getString("prompt", "默认提示词");

            JsonObject response = new JsonObject()
                .put("success", true)
                .put("code", 200)
                .put("message", "文生图任务创建成功")
                .put("data", new JsonObject()
                    .put("id", System.currentTimeMillis())
                    .put("prompt", prompt)
                    .put("status", "PENDING")
                    .put("created_at", System.currentTimeMillis()));

            context.response()
                .putHeader("Content-Type", "application/json")
                .end(response.encode());
        });

        // 创建图生图任务
        router.post("/ai/image-to-image").handler(context -> {
            JsonObject requestBody = context.body().asJsonObject();
            String prompt = requestBody.getString("prompt", "默认提示词");

            JsonObject response = new JsonObject()
                .put("success", true)
                .put("code", 200)
                .put("message", "图生图任务创建成功")
                .put("data", new JsonObject()
                    .put("id", System.currentTimeMillis())
                    .put("prompt", prompt)
                    .put("status", "PENDING"));

            context.response()
                .putHeader("Content-Type", "application/json")
                .end(response.encode());
        });

        // 获取公开的图片生成记录
        router.get("/ai/generations/public").handler(context -> {
            JsonObject response = new JsonObject()
                .put("success", true)
                .put("code", 200)
                .put("message", "获取成功")
                .put("data", new io.vertx.core.json.JsonArray()
                    .add(new JsonObject()
                        .put("id", 1)
                        .put("prompt", "一只可爱的橘猫")
                        .put("status", "COMPLETED")
                        .put("generated_image_url", "https://example.com/cat.jpg"))
                    .add(new JsonObject()
                        .put("id", 2)
                        .put("prompt", "美丽的风景")
                        .put("status", "COMPLETED")
                        .put("generated_image_url", "https://example.com/landscape.jpg")));

            context.response()
                .putHeader("Content-Type", "application/json")
                .end(response.encode());
        });

        // 搜索图片生成记录
        router.get("/ai/generations/search").handler(context -> {
            String keyword = context.request().getParam("keyword");
            
            JsonObject response = new JsonObject()
                .put("success", true)
                .put("code", 200)
                .put("message", "搜索完成")
                .put("data", new io.vertx.core.json.JsonArray()
                    .add(new JsonObject()
                        .put("id", 1)
                        .put("prompt", "搜索结果: " + keyword)
                        .put("status", "COMPLETED")));

            context.response()
                .putHeader("Content-Type", "application/json")
                .end(response.encode());
        });
    }

    /**
     * 配置通义千问AI路由
     */
    private void configureQwenRoutes(Router router) {
        // 智能问答
        router.post("/ai/chat").handler(context -> {
            JsonObject requestBody = context.body().asJsonObject();
            String question = requestBody.getString("question", "你好");
            
            String answer = "这是通义千问的模拟回答：" + question + "。实际使用时会调用真实的API。";
            
            JsonObject response = new JsonObject()
                .put("success", true)
                .put("code", 200)
                .put("message", "问答成功")
                .put("data", new JsonObject()
                    .put("question", question)
                    .put("answer", answer)
                    .put("timestamp", System.currentTimeMillis()));
            
            context.response()
                .putHeader("Content-Type", "application/json")
                .end(response.encode());
        });

        // 生成博客文章
        router.post("/ai/generate-article").handler(context -> {
            JsonObject requestBody = context.body().asJsonObject();
            String topic = requestBody.getString("topic", "技术文章");
            
            String article = "# " + topic + "\n\n这是由通义千问生成的技术文章示例。\n\n## 简介\n\n" +
                "本文将介绍" + topic + "的相关内容...\n\n## 详细内容\n\n待实际API集成后生成完整内容。";
            
            JsonObject response = new JsonObject()
                .put("success", true)
                .put("code", 200)
                .put("message", "文章生成成功")
                .put("data", new JsonObject()
                    .put("topic", topic)
                    .put("article", article)
                    .put("wordCount", article.length()));
            
            context.response()
                .putHeader("Content-Type", "application/json")
                .end(response.encode());
        });

        // 优化绘画提示词
        router.post("/ai/optimize-prompt").handler(context -> {
            JsonObject requestBody = context.body().asJsonObject();
            String input = requestBody.getString("input", "一只猫");
            
            String optimizedPrompt = "A beautiful cat sitting gracefully, detailed fur texture, " +
                "soft lighting, high quality, photorealistic, 4k resolution";
            
            JsonObject response = new JsonObject()
                .put("success", true)
                .put("code", 200)
                .put("message", "提示词优化成功")
                .put("data", new JsonObject()
                    .put("originalInput", input)
                    .put("optimizedPrompt", optimizedPrompt));
            
            context.response()
                .putHeader("Content-Type", "application/json")
                .end(response.encode());
        });

        // API连接测试
        router.get("/ai/qwen/test").handler(context -> {
            JsonObject response = new JsonObject()
                .put("success", true)
                .put("code", 200)
                .put("message", "通义千问API连接正常（模拟）")
                .put("data", new JsonObject()
                    .put("connected", true)
                    .put("apiKey", "sk-4606dfde828a4f9aa7a43f5d53dddb9e")
                    .put("model", "qwen-turbo"));
            
            context.response()
                .putHeader("Content-Type", "application/json")
                .end(response.encode());
        });
    }

    /**
     * 配置其他模块路由
     */
    private void configureOtherRoutes(Router router) {
        // 视频模块
        router.get("/videos").handler(context -> {
            JsonObject response = new JsonObject()
                .put("success", true)
                .put("message", "视频功能开发中")
                .put("data", new io.vertx.core.json.JsonArray());
            context.response()
                .putHeader("Content-Type", "application/json")
                .end(response.encode());
        });

        // 文章模块
        router.get("/articles").handler(context -> {
            JsonObject response = new JsonObject()
                .put("success", true)
                .put("message", "文章功能开发中")
                .put("data", new io.vertx.core.json.JsonArray());
            context.response()
                .putHeader("Content-Type", "application/json")
                .end(response.encode());
        });

        // 项目模块
        router.get("/projects").handler(context -> {
            JsonObject response = new JsonObject()
                .put("success", true)
                .put("message", "项目功能开发中")
                .put("data", new io.vertx.core.json.JsonArray());
            context.response()
                .putHeader("Content-Type", "application/json")
                .end(response.encode());
        });
    }

    /**
     * 配置错误处理
     */
    private void configureErrorHandling(Router router) {
        // 404处理
        router.route().last().handler(context -> {
            JsonObject response = new JsonObject()
                .put("success", false)
                .put("code", 404)
                .put("message", "API接口不存在")
                .put("timestamp", System.currentTimeMillis());

            context.response()
                .setStatusCode(404)
                .putHeader("Content-Type", "application/json")
                .end(response.encode());
        });

        // 全局异常处理
        router.route().failureHandler(context -> {
            Throwable failure = context.failure();
            int statusCode = context.statusCode();
            
            if (statusCode == -1) {
                statusCode = 500;
            }

            String message = "服务器内部错误";
            if (failure != null) {
                message = failure.getMessage();
                logger.error("请求处理失败", failure);
            }

            JsonObject response = new JsonObject()
                .put("success", false)
                .put("code", statusCode)
                .put("message", message)
                .put("timestamp", System.currentTimeMillis());

            context.response()
                .setStatusCode(statusCode)
                .putHeader("Content-Type", "application/json")
                .end(response.encode());
        });
    }
}
