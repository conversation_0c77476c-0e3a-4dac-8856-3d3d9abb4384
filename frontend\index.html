<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    
    <title>SuperBlog - 个人技术展示平台</title>
    <meta name="description" content="SuperBlog是一个现代化的个人技术展示平台，集成AI图片生成、视频展示、博客、项目展示等功能。" />
    <meta name="keywords" content="技术博客,AI图片生成,项目展示,个人网站,React,TypeScript" />
    <meta name="author" content="SuperBlog" />
    
    <meta property="og:title" content="SuperBlog - 个人技术展示平台" />
    <meta property="og:description" content="现代化的个人技术展示平台，集成AI功能、博客、项目展示等" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://superblog.example.com" />
    <meta property="og:image" content="/og-image.jpg" />
    
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="SuperBlog - 个人技术展示平台" />
    <meta name="twitter:description" content="现代化的个人技术展示平台，集成AI功能、博客、项目展示等" />
    <meta name="twitter:image" content="/twitter-image.jpg" />
    
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="theme-color" content="#3b82f6" />
    
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet" />
    
    <style>
      html {
        visibility: hidden;
        opacity: 0;
      }
      
      .loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        transition: opacity 0.5s ease-out;
      }
      
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: white;
        animation: spin 1s ease-in-out infinite;
      }
      
      .loading-text {
        color: white;
        font-family: 'Inter', sans-serif;
        font-size: 18px;
        font-weight: 500;
        margin-top: 20px;
      }
      
      @keyframes spin {
        to {
          transform: rotate(360deg);
        }
      }
      
      @media (prefers-color-scheme: dark) {
        .loading-screen {
          background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
        }
      }
    </style>
    
    <script>
      (function() {
        const theme = localStorage.getItem('superblog-app-store');
        let isDark = false;
        
        if (theme) {
          try {
            const parsed = JSON.parse(theme);
            const themeValue = parsed.state?.config?.theme;
            if (themeValue === 'dark') {
              isDark = true;
            } else if (themeValue === 'system') {
              isDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            }
          } catch (e) {
            isDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
          }
        } else {
          isDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        }
        
        if (isDark) {
          document.documentElement.classList.add('dark');
        }
        
        document.documentElement.style.visibility = 'visible';
        document.documentElement.style.opacity = '1';
      })();
    </script>
  </head>
  
  <body>
    <div id="loading-screen" class="loading-screen">
      <div style="text-align: center;">
        <div class="loading-spinner"></div>
        <div class="loading-text">SuperBlog 加载中...</div>
      </div>
    </div>
    
    <div id="root"></div>
    
    <script type="module" src="/src/main.tsx"></script>
    
    <script>
      window.addEventListener('load', function() {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
          setTimeout(() => {
            loadingScreen.style.opacity = '0';
            setTimeout(() => {
              loadingScreen.remove();
            }, 500);
          }, 1000);
        }
      });
      
      window.addEventListener('error', function(e) {
        console.error('应用启动错误:', e.error);
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
          loadingScreen.innerHTML = '<div style="text-align: center; color: white;"><h2 style="margin-bottom: 10px;">应用启动失败</h2><p style="margin-bottom: 20px;">请刷新页面重试</p><button onclick="location.reload()" style="background: white; color: #667eea; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; font-weight: 500;">刷新页面</button></div>';
        }
      });
    </script>
    
    <noscript>
      <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: #f3f4f6; display: flex; align-items: center; justify-content: center; font-family: 'Inter', sans-serif; text-align: center; z-index: 10000;">
        <div>
          <h1 style="color: #374151; margin-bottom: 20px;">需要启用JavaScript</h1>
          <p style="color: #6b7280; margin-bottom: 30px;">SuperBlog需要JavaScript才能正常运行，请在浏览器设置中启用JavaScript。</p>
          <a href="https://enable-javascript.com/zh/" target="_blank" style="background: #3b82f6; color: white; text-decoration: none; padding: 12px 24px; border-radius: 6px; font-weight: 500;">了解如何启用JavaScript</a>
        </div>
      </div>
    </noscript>
  </body>
</html>
