#!/bin/bash

# SuperBlog Backend 快速测试脚本

echo "=== SuperBlog Backend 快速测试 ==="

BASE_URL="http://localhost:8080/api"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 等待服务启动
echo -e "${YELLOW}等待服务启动...${NC}"
for i in {1..30}; do
    if curl -s "$BASE_URL/health" > /dev/null 2>&1; then
        echo -e "${GREEN}服务已启动${NC}"
        break
    fi
    echo -n "."
    sleep 1
done

echo ""

# 测试健康检查
echo -e "${YELLOW}测试健康检查...${NC}"
response=$(curl -s "$BASE_URL/health")
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ 健康检查通过${NC}"
    echo "响应: $response"
else
    echo -e "${RED}✗ 健康检查失败${NC}"
    exit 1
fi

# 测试AI图片生成接口
echo -e "\n${YELLOW}测试AI图片生成接口...${NC}"
response=$(curl -s -w "%{http_code}" -X POST \
    -H "Content-Type: application/json" \
    -d '{
        "user_id": 1,
        "prompt": "测试提示词",
        "width": 512,
        "height": 512
    }' \
    "$BASE_URL/ai/text-to-image")

status=$(echo "$response" | tail -c 4)
body=$(echo "$response" | head -c -4)

if [ "$status" = "200" ]; then
    echo -e "${GREEN}✓ AI图片生成接口测试通过${NC}"
else
    echo -e "${YELLOW}⚠ AI图片生成接口返回状态码: $status${NC}"
    echo "响应: $body"
fi

# 测试获取公开图片生成记录
echo -e "\n${YELLOW}测试获取公开图片生成记录...${NC}"
response=$(curl -s -w "%{http_code}" "$BASE_URL/ai/generations/public?page=0&size=5")

status=$(echo "$response" | tail -c 4)
body=$(echo "$response" | head -c -4)

if [ "$status" = "200" ]; then
    echo -e "${GREEN}✓ 获取公开图片生成记录测试通过${NC}"
else
    echo -e "${YELLOW}⚠ 获取公开图片生成记录返回状态码: $status${NC}"
fi

# 测试404错误处理
echo -e "\n${YELLOW}测试404错误处理...${NC}"
response=$(curl -s -w "%{http_code}" "$BASE_URL/nonexistent")

status=$(echo "$response" | tail -c 4)

if [ "$status" = "404" ]; then
    echo -e "${GREEN}✓ 404错误处理测试通过${NC}"
else
    echo -e "${RED}✗ 404错误处理测试失败，状态码: $status${NC}"
fi

echo -e "\n${GREEN}=== 快速测试完成 ===${NC}"
echo -e "如需完整测试，请运行: ${YELLOW}./api-tests.sh${NC}"
