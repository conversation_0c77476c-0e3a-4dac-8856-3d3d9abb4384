package com.superblog.infrastructure.database;

import com.superblog.config.DatabaseConfig;
import io.vertx.core.Vertx;
import io.vertx.pgclient.PgConnectOptions;
import io.vertx.pgclient.PgPool;
import io.vertx.sqlclient.PoolOptions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 数据库连接池工厂类
 * 负责创建和配置PostgreSQL连接池
 */
public class DatabasePool {

    private static final Logger logger = LoggerFactory.getLogger(DatabasePool.class);

    /**
     * 创建PostgreSQL连接池
     */
    public static PgPool create(Vertx vertx, DatabaseConfig config) {
        logger.info("正在创建PostgreSQL连接池...");

        // 配置数据库连接选项
        PgConnectOptions connectOptions = new PgConnectOptions()
            .setPort(config.port())
            .setHost(config.host())
            .setDatabase(config.database())
            .setUser(config.username())
            .setPassword(config.password())
            .setConnectTimeout(config.connectTimeout())
            .setIdleTimeout(config.idleTimeout())
            .setReconnectAttempts(3)
            .setReconnectInterval(1000);

        // 配置连接池选项
        PoolOptions poolOptions = new PoolOptions()
            .setMaxSize(config.maxPoolSize())
            .setMaxWaitQueueSize(100);

        PgPool pool = PgPool.pool(vertx, connectOptions, poolOptions);
        
        logger.info("PostgreSQL连接池创建完成，最大连接数: {}", config.maxPoolSize());
        
        return pool;
    }
}
