package com.superblog.config;

import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import java.util.List;

/**
 * 应用程序配置类
 * 负责管理和提供各种配置信息
 */
public class AppConfig {

    private final JsonObject config;

    public AppConfig(JsonObject config) {
        this.config = config;
    }

    /**
     * 获取服务器配置
     */
    public JsonObject getServerConfig() {
        return config.getJsonObject("server", new JsonObject());
    }

    /**
     * 获取数据库配置
     */
    public DatabaseConfig getDatabaseConfig() {
        JsonObject dbConfig = config.getJsonObject("database", new JsonObject());
        return new DatabaseConfig(
            dbConfig.getString("host", "localhost"),
            dbConfig.getInteger("port", 5432),
            dbConfig.getString("database", "superblog"),
            dbConfig.getString("username", "superblog"),
            dbConfig.getString("password", "superblog123"),
            dbConfig.getInteger("maxPoolSize", 20),
            dbConfig.getInteger("connectTimeout", 5000),
            dbConfig.getInteger("idleTimeout", 300000)
        );
    }

    /**
     * 获取Redis配置
     */
    public RedisConfig getRedisConfig() {
        JsonObject redisConfig = config.getJsonObject("redis", new JsonObject());
        return new RedisConfig(
            redisConfig.getString("host", "localhost"),
            redisConfig.getInteger("port", 6379),
            redisConfig.getString("password"),
            redisConfig.getInteger("database", 0),
            redisConfig.getInteger("maxPoolSize", 10),
            redisConfig.getInteger("maxWaitingHandlers", 100)
        );
    }

    /**
     * 获取JWT配置
     */
    public JwtConfig getJwtConfig() {
        JsonObject jwtConfig = config.getJsonObject("jwt", new JsonObject());
        return new JwtConfig(
            jwtConfig.getString("secret", "default-secret"),
            jwtConfig.getLong("expiration", 86400000L)
        );
    }

    /**
     * 获取AI配置
     */
    public AiConfig getAiConfig() {
        JsonObject aiConfig = config.getJsonObject("ai", new JsonObject());
        
        // OpenAI配置
        JsonObject openaiConfig = aiConfig.getJsonObject("openai", new JsonObject());
        AiConfig.OpenAiConfig openAiConfig = new AiConfig.OpenAiConfig(
            openaiConfig.getString("api-key", ""),
            openaiConfig.getString("base-url", "https://api.openai.com/v1"),
            openaiConfig.getString("model", "gpt-3.5-turbo"),
            openaiConfig.getInteger("timeout", 60000)
        );
        
        // Stability AI配置
        JsonObject stabilityConfig = aiConfig.getJsonObject("stability", new JsonObject());
        AiConfig.StabilityAiConfig stabilityAiConfig = new AiConfig.StabilityAiConfig(
            stabilityConfig.getString("api-key", ""),
            stabilityConfig.getString("base-url", "https://api.stability.ai/v1"),
            stabilityConfig.getInteger("timeout", 120000)
        );
        
        // 通义千问配置
        JsonObject qwenConfig = aiConfig.getJsonObject("qwen", new JsonObject());
        AiConfig.QwenConfig qwenAiConfig = new AiConfig.QwenConfig(
            qwenConfig.getString("api-key", ""),
            qwenConfig.getString("base-url", "https://dashscope.aliyuncs.com/api/v1"),
            qwenConfig.getString("model", "qwen-turbo"),
            qwenConfig.getInteger("timeout", 60000)
        );
        
        return new AiConfig(openAiConfig, stabilityAiConfig, qwenAiConfig);
    }

    /**
     * 获取文件配置
     */
    public FileConfig getFileConfig() {
        JsonObject fileConfig = config.getJsonObject("file", new JsonObject());
        JsonObject uploadConfig = fileConfig.getJsonObject("upload", new JsonObject());
        
        FileConfig.UploadConfig upload = new FileConfig.UploadConfig(
            uploadConfig.getString("path", "./uploads"),
            uploadConfig.getString("max-size", "10MB"),
            uploadConfig.getString("allowed-types", "jpg,jpeg,png,gif,webp")
        );
        
        return new FileConfig(upload);
    }

    /**
     * 获取CORS配置
     */
    public CorsConfig getCorsConfig() {
        JsonObject corsConfig = config.getJsonObject("cors", new JsonObject());
        return new CorsConfig(
            corsConfig.getString("allowed-origins", "*"),
            corsConfig.getString("allowed-methods", "GET,POST,PUT,DELETE,OPTIONS"),
            corsConfig.getString("allowed-headers", "*"),
            corsConfig.getLong("max-age", 3600L)
        );
    }

    /**
     * 获取日志配置
     */
    public JsonObject getLoggingConfig() {
        return config.getJsonObject("logging", new JsonObject());
    }

    /**
     * 获取原始配置对象
     */
    public JsonObject getRawConfig() {
        return config;
    }
}
