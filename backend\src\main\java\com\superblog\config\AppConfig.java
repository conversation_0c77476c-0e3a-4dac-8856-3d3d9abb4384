package com.superblog.config;

import io.vertx.core.json.JsonObject;

/**
 * 应用程序配置类
 * 负责管理和提供各种配置信息
 */
public class AppConfig {

    private final JsonObject config;

    public AppConfig(JsonObject config) {
        this.config = config;
    }

    /**
     * 获取服务器配置
     */
    public JsonObject getServerConfig() {
        return config.getJsonObject("server", new JsonObject());
    }

    /**
     * 获取数据库配置
     */
    public DatabaseConfig getDatabaseConfig() {
        JsonObject dbConfig = config.getJsonObject("database", new JsonObject());
        return new DatabaseConfig(
            dbConfig.getString("host", "localhost"),
            dbConfig.getInteger("port", 5432),
            dbConfig.getString("database", "superblog"),
            dbConfig.getString("username", "superblog"),
            dbConfig.getString("password", "superblog123"),
            dbConfig.getInteger("maxPoolSize", 20),
            dbConfig.getInteger("connectTimeout", 5000),
            dbConfig.getInteger("idleTimeout", 300000)
        );
    }

    /**
     * 获取Redis配置
     */
    public RedisConfig getRedisConfig() {
        JsonObject redisConfig = config.getJsonObject("redis", new JsonObject());
        return new RedisConfig(
            redisConfig.getString("host", "localhost"),
            redisConfig.getInteger("port", 6379),
            redisConfig.getString("password"),
            redisConfig.getInteger("database", 0),
            redisConfig.getInteger("maxPoolSize", 10),
            redisConfig.getInteger("maxWaitingHandlers", 100)
        );
    }

    /**
     * 获取JWT配置
     */
    public JwtConfig getJwtConfig() {
        JsonObject jwtConfig = config.getJsonObject("jwt", new JsonObject());
        return new JwtConfig(
            jwtConfig.getString("secret", "default-secret"),
            jwtConfig.getInteger("expirationTime", 86400)
        );
    }

    /**
     * 获取AI配置
     */
    public AiConfig getAiConfig() {
        JsonObject aiConfig = config.getJsonObject("ai", new JsonObject());
        JsonObject openaiConfig = aiConfig.getJsonObject("openai", new JsonObject());
        JsonObject stabilityConfig = aiConfig.getJsonObject("stabilityai", new JsonObject());
        JsonObject qwenConfig = aiConfig.getJsonObject("qwen", new JsonObject());

        return new AiConfig(
            new AiConfig.OpenAiConfig(
                openaiConfig.getString("apiKey"),
                openaiConfig.getString("baseUrl", "https://api.openai.com/v1"),
                openaiConfig.getString("model", "dall-e-3"),
                openaiConfig.getInteger("timeout", 60000)
            ),
            new AiConfig.StabilityAiConfig(
                stabilityConfig.getString("apiKey"),
                stabilityConfig.getString("baseUrl", "https://api.stability.ai/v1"),
                stabilityConfig.getInteger("timeout", 60000)
            ),
            new AiConfig.QwenConfig(
                qwenConfig.getString("apiKey"),
                qwenConfig.getString("baseUrl", "https://dashscope.aliyuncs.com/api/v1"),
                qwenConfig.getString("model", "qwen-turbo"),
                qwenConfig.getInteger("timeout", 60000)
            )
        );
    }

    /**
     * 获取文件配置
     */
    public FileConfig getFileConfig() {
        JsonObject fileConfig = config.getJsonObject("file", new JsonObject());
        return new FileConfig(
            fileConfig.getString("uploadPath", "./uploads"),
            fileConfig.getLong("maxFileSize", 10485760L),
            fileConfig.getJsonArray("allowedTypes").getList()
        );
    }

    /**
     * 获取CORS配置
     */
    public CorsConfig getCorsConfig() {
        JsonObject corsConfig = config.getJsonObject("cors", new JsonObject());
        return new CorsConfig(
            corsConfig.getJsonArray("allowedOrigins").getList(),
            corsConfig.getJsonArray("allowedMethods").getList(),
            corsConfig.getJsonArray("allowedHeaders").getList(),
            corsConfig.getBoolean("allowCredentials", true)
        );
    }

    /**
     * 获取日志配置
     */
    public JsonObject getLoggingConfig() {
        return config.getJsonObject("logging", new JsonObject());
    }

    /**
     * 获取原始配置对象
     */
    public JsonObject getRawConfig() {
        return config;
    }
}
