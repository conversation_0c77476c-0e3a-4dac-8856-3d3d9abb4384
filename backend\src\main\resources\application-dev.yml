server:
  port: 8080
  servlet:
    context-path: /api

# 线程池配置
thread:
  pool:
    executor:
      config:
        core-pool-size: 10
        max-pool-size: 20
        keep-alive-time: 5000
        block-queue-size: 2000
        policy: CallerRunsPolicy

# 数据库配置
spring:
  datasource:
    username: superblog
    password: superblog123
    url: **************************************************************************************************************
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
      idle-timeout: 30000
      connection-timeout: 30000
      max-lifetime: 1800000
  
  # Redis配置
  redis:
    host: localhost
    port: 6379
    password: 
    timeout: 5000
    lettuce:
      pool:
        max-active: 10
        max-idle: 8
        min-idle: 2
        max-wait: -1
  
  main:
    allow-bean-definition-overriding: true

# AI服务配置
ai:
  # OpenAI配置
  openai:
    base-url: https://api.openai.com/v1
    api-key: ${OPENAI_API_KEY:}
    model: gpt-3.5-turbo
    timeout: 60000
    
  # 通义千问配置  
  qwen:
    base-url: https://dashscope.aliyuncs.com/api/v1
    api-key: ${QWEN_API_KEY:}
    model: qwen-turbo
    timeout: 60000
    
  # Stability AI配置
  stability:
    base-url: https://api.stability.ai/v1
    api-key: ${STABILITY_API_KEY:}
    timeout: 120000

# 文件存储配置
file:
  upload:
    path: ./uploads
    max-size: 10MB
    allowed-types: jpg,jpeg,png,gif,webp,pdf,doc,docx
  
# JWT配置
jwt:
  secret: ${JWT_SECRET:superblog-dev-secret-key-2024}
  expiration: 86400000 # 24小时

# CORS配置
cors:
  allowed-origins: http://localhost:3000,http://127.0.0.1:3000
  allowed-methods: GET,POST,PUT,DELETE,OPTIONS
  allowed-headers: "*"
  max-age: 3600

# 日志配置
logging:
  level:
    root: info
    com.superblog: debug
  config: classpath:logback.xml