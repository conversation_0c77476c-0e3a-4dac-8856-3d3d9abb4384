#!/bin/bash

# SuperBlog API 测试脚本
# 包含所有API接口的curl测试命令

BASE_URL="http://localhost:8080/api"

echo "=== SuperBlog API 测试开始 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试函数
test_api() {
    local name="$1"
    local method="$2"
    local url="$3"
    local data="$4"
    local expected_status="$5"
    
    echo -e "\n${YELLOW}测试: $name${NC}"
    echo "请求: $method $url"
    
    if [ -n "$data" ]; then
        echo "数据: $data"
        response=$(curl -s -w "\n%{http_code}" -X "$method" \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$url")
    else
        response=$(curl -s -w "\n%{http_code}" -X "$method" "$url")
    fi
    
    # 分离响应体和状态码
    body=$(echo "$response" | head -n -1)
    status=$(echo "$response" | tail -n 1)
    
    echo "状态码: $status"
    echo "响应: $body"
    
    if [ "$status" = "$expected_status" ]; then
        echo -e "${GREEN}✓ 测试通过${NC}"
    else
        echo -e "${RED}✗ 测试失败 (期望: $expected_status, 实际: $status)${NC}"
    fi
}

# 1. 健康检查
echo -e "\n${YELLOW}=== 1. 基础功能测试 ===${NC}"
test_api "健康检查" "GET" "$BASE_URL/health" "" "200"

# 2. AI图片生成相关测试
echo -e "\n${YELLOW}=== 2. AI图片生成功能测试 ===${NC}"

# 2.1 创建文生图任务
test_api "创建文生图任务" "POST" "$BASE_URL/ai/text-to-image" '{
    "user_id": 1,
    "prompt": "一只可爱的橘猫坐在樱花树下，动漫风格，高清画质",
    "negative_prompt": "低质量，模糊",
    "width": 512,
    "height": 512,
    "style": "动漫风格",
    "model": "dall-e-3",
    "steps": 20,
    "cfg_scale": 7.0,
    "is_public": true
}' "200"

# 2.2 创建图生图任务
test_api "创建图生图任务" "POST" "$BASE_URL/ai/image-to-image" '{
    "user_id": 1,
    "prompt": "将这张图片转换为油画风格",
    "original_image_url": "https://example.com/original.jpg",
    "width": 512,
    "height": 512,
    "style": "油画风格",
    "steps": 20,
    "cfg_scale": 7.0,
    "is_public": true
}' "200"

# 2.3 获取公开的图片生成记录
test_api "获取公开图片生成记录" "GET" "$BASE_URL/ai/generations/public?page=0&size=10" "" "200"

# 2.4 获取热门图片生成记录
test_api "获取热门图片生成记录" "GET" "$BASE_URL/ai/generations/popular?limit=5" "" "200"

# 2.5 获取最新图片生成记录
test_api "获取最新图片生成记录" "GET" "$BASE_URL/ai/generations/latest?limit=5" "" "200"

# 2.6 搜索图片生成记录
test_api "搜索图片生成记录" "GET" "$BASE_URL/ai/generations/search?keyword=猫&page=0&size=10" "" "200"

# 2.7 获取用户图片生成记录
test_api "获取用户图片生成记录" "GET" "$BASE_URL/ai/generations/user/1?page=0&size=10" "" "200"

# 2.8 获取图片生成记录详情 (假设ID为1)
test_api "获取图片生成记录详情" "GET" "$BASE_URL/ai/generations/1" "" "200"

# 2.9 点赞图片生成记录
test_api "点赞图片生成记录" "POST" "$BASE_URL/ai/generations/1/like" "" "200"

# 2.10 删除图片生成记录
test_api "删除图片生成记录" "DELETE" "$BASE_URL/ai/generations/1" "" "200"

# 3. 视频相关测试
echo -e "\n${YELLOW}=== 3. 视频功能测试 ===${NC}"
test_api "获取视频列表" "GET" "$BASE_URL/videos" "" "200"
test_api "获取视频详情" "GET" "$BASE_URL/videos/1" "" "200"

# 4. 文章相关测试
echo -e "\n${YELLOW}=== 4. 文章功能测试 ===${NC}"
test_api "获取文章列表" "GET" "$BASE_URL/articles" "" "200"
test_api "获取文章详情" "GET" "$BASE_URL/articles/1" "" "200"

# 5. 项目相关测试
echo -e "\n${YELLOW}=== 5. 项目功能测试 ===${NC}"
test_api "获取项目列表" "GET" "$BASE_URL/projects" "" "200"
test_api "获取项目详情" "GET" "$BASE_URL/projects/1" "" "200"

# 6. 用户相关测试
echo -e "\n${YELLOW}=== 6. 用户功能测试 ===${NC}"
test_api "获取用户资料" "GET" "$BASE_URL/users/profile" "" "200"
test_api "更新用户资料" "PUT" "$BASE_URL/users/profile" '{
    "username": "testuser",
    "email": "<EMAIL>",
    "bio": "这是一个测试用户"
}' "200"

# 7. 错误处理测试
echo -e "\n${YELLOW}=== 7. 错误处理测试 ===${NC}"
test_api "访问不存在的接口" "GET" "$BASE_URL/nonexistent" "" "404"
test_api "无效的请求参数" "POST" "$BASE_URL/ai/text-to-image" '{"invalid": "data"}' "400"

echo -e "\n${GREEN}=== SuperBlog API 测试完成 ===${NC}"

# 性能测试示例
echo -e "\n${YELLOW}=== 8. 性能测试示例 ===${NC}"
echo "并发测试健康检查接口 (10个并发请求):"
for i in {1..10}; do
    curl -s "$BASE_URL/health" > /dev/null &
done
wait
echo "并发测试完成"

# 压力测试示例 (需要安装ab工具)
if command -v ab &> /dev/null; then
    echo -e "\n使用Apache Bench进行压力测试:"
    echo "ab -n 100 -c 10 $BASE_URL/health"
    ab -n 100 -c 10 "$BASE_URL/health"
else
    echo -e "\n${YELLOW}提示: 安装Apache Bench (apt-get install apache2-utils) 可进行更详细的性能测试${NC}"
fi

echo -e "\n${GREEN}所有测试完成！${NC}"
