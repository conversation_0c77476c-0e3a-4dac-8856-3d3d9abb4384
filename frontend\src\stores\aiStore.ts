/**
 * AI功能状态管理
 * 管理AI图片生成和通义千问相关的状态
 */

import { create } from 'zustand';
import type { 
  ImageGeneration, 
  ImageGenerationRequest,
  QwenChatRequest,
  QwenChatResponse,
  QwenArticleRequest,
  QwenArticleResponse 
} from '@/types/api';
import { imageApi, qwenApi } from '@/services/api';

// AI图片生成状态
interface ImageGenerationState {
  // 数据状态
  generations: ImageGeneration[];
  currentGeneration: ImageGeneration | null;
  
  // UI状态
  loading: boolean;
  error: string | null;
  
  // 表单状态
  formData: Partial<ImageGenerationRequest>;
  
  // 分页状态
  pagination: {
    current: number;
    total: number;
    pageSize: number;
  };
  
  // 操作方法
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setGenerations: (generations: ImageGeneration[]) => void;
  setCurrentGeneration: (generation: ImageGeneration | null) => void;
  updateFormData: (data: Partial<ImageGenerationRequest>) => void;
  resetFormData: () => void;
  
  // API操作
  createTextToImage: (data: ImageGenerationRequest) => Promise<ImageGeneration>;
  loadPublicGenerations: (page?: number) => Promise<void>;
  loadPopularGenerations: () => Promise<void>;
  searchGenerations: (keyword: string) => Promise<void>;
  likeGeneration: (id: number) => Promise<void>;
  pollGenerationStatus: (id: number) => Promise<void>;
}

// 通义千问状态
interface QwenState {
  // 聊天状态
  chatHistory: QwenChatResponse[];
  currentQuestion: string;
  chatLoading: boolean;
  
  // 文章生成状态
  generatedArticle: QwenArticleResponse | null;
  articleLoading: boolean;
  
  // 提示词优化状态
  optimizedPrompt: string;
  promptLoading: boolean;
  
  // 通用状态
  error: string | null;
  
  // 操作方法
  setError: (error: string | null) => void;
  setChatLoading: (loading: boolean) => void;
  setArticleLoading: (loading: boolean) => void;
  setPromptLoading: (loading: boolean) => void;
  
  // API操作
  sendChatMessage: (data: QwenChatRequest) => Promise<void>;
  generateArticle: (data: QwenArticleRequest) => Promise<void>;
  optimizePrompt: (input: string) => Promise<string>;
  clearChatHistory: () => void;
}

// 默认表单数据
const defaultFormData: Partial<ImageGenerationRequest> = {
  prompt: '',
  negative_prompt: '',
  width: 512,
  height: 512,
  style: '写实风格',
  model: 'dall-e-3',
  steps: 20,
  cfg_scale: 7.0,
  is_public: true,
};

// AI图片生成Store
export const useImageStore = create<ImageGenerationState>()((set, get) => ({
    // 初始状态
    generations: [],
    currentGeneration: null,
    loading: false,
    error: null,
    formData: defaultFormData,
    pagination: {
      current: 1,
      total: 0,
      pageSize: 20,
    },
    
    // 基础操作
    setLoading: (loading) => set({ loading }),
    setError: (error) => set({ error }),
    setGenerations: (generations) => set({ generations }),
    setCurrentGeneration: (generation) => set({ currentGeneration: generation }),
    
    updateFormData: (data) => set((state) => ({
      ...state,
      formData: { ...state.formData, ...data }
    })),
    
    resetFormData: () => set({ formData: defaultFormData }),
    
    // API操作
    createTextToImage: async (data) => {
      set({ loading: true, error: null });
      try {
        const result = await imageApi.createTextToImage(data);
        set((state) => ({
          ...state,
          generations: [result, ...state.generations],
          currentGeneration: result,
          loading: false
        }));
        
        // 如果任务还在处理中，开始轮询状态
        if (result.status === 'PENDING' || result.status === 'PROCESSING') {
          get().pollGenerationStatus(result.id);
        }
        
        return result;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '创建失败';
        set({ error: errorMessage, loading: false });
        throw error;
      }
    },
    
    // 轮询图片生成状态
    pollGenerationStatus: async (id: number) => {
      const maxAttempts = 30; // 最多轮询30次 (约5分钟)
      let attempts = 0;
      
      const poll = async () => {
        try {
          attempts++;
          const result = await imageApi.getGenerationById(id);
          
          if (result) {
            // 更新当前生成结果
            set((state) => ({
              ...state,
              currentGeneration: result,
              generations: state.generations.map(gen => 
                gen.id === id ? result : gen
              )
            }));
            
            // 如果完成或失败，停止轮询
            if (result.status === 'COMPLETED' || result.status === 'FAILED') {
              if (result.status === 'FAILED') {
                set({ error: result.error_message || '图片生成失败' });
              }
              return;
            }
            
            // 继续轮询
            if (attempts < maxAttempts) {
              setTimeout(() => poll(), 10000); // 10秒后重试
            } else {
              set({ error: '图片生成超时，请稍后刷新查看结果' });
            }
          }
        } catch (error) {
          console.error('轮询图片生成状态失败:', error);
          if (attempts < maxAttempts) {
            setTimeout(() => poll(), 10000); // 10秒后重试
          }
        }
      };
      
      // 延迟3秒后开始第一次轮询
      setTimeout(() => poll(), 3000);
    },
    
    loadPublicGenerations: async (page = 1) => {
      set({ loading: true, error: null });
      try {
        const result = await imageApi.getPublicGenerations({ 
          page: page - 1, 
          size: get().pagination.pageSize 
        });
        set((state) => ({
          ...state,
          generations: result.content,
          pagination: {
            current: page,
            total: result.totalElements,
            pageSize: result.size,
          },
          loading: false
        }));
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '加载失败';
        set({ error: errorMessage, loading: false });
      }
    },
    
    loadPopularGenerations: async () => {
      set({ loading: true, error: null });
      try {
        const result = await imageApi.getPopularGenerations(10);
        set({ generations: result, loading: false });
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '加载失败';
        set({ error: errorMessage, loading: false });
      }
    },
    
    searchGenerations: async (keyword) => {
      set({ loading: true, error: null });
      try {
        const result = await imageApi.searchGenerations(keyword, { 
          page: 0, 
          size: get().pagination.pageSize 
        });
        set((state) => ({
          ...state,
          generations: result.content,
          pagination: {
            current: 1,
            total: result.totalElements,
            pageSize: result.size,
          },
          loading: false
        }));
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '搜索失败';
        set({ error: errorMessage, loading: false });
      }
    },
    
    likeGeneration: async (id) => {
      try {
        await imageApi.likeGeneration(id);
        set((state) => ({
          ...state,
          generations: state.generations.map(g =>
            g.id === id ? { ...g, like_count: g.like_count + 1 } : g
          ),
          currentGeneration: state.currentGeneration?.id === id
            ? { ...state.currentGeneration, like_count: state.currentGeneration.like_count + 1 }
            : state.currentGeneration
        }));
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '点赞失败';
        set({ error: errorMessage });
      }
    },
  }));

// 通义千问Store
export const useQwenStore = create<QwenState>()((set) => ({
    // 初始状态
    chatHistory: [],
    currentQuestion: '',
    chatLoading: false,
    generatedArticle: null,
    articleLoading: false,
    optimizedPrompt: '',
    promptLoading: false,
    error: null,
    
    // 基础操作
    setError: (error) => set({ error }),
    setChatLoading: (loading) => set({ chatLoading: loading }),
    setArticleLoading: (loading) => set({ articleLoading: loading }),
    setPromptLoading: (loading) => set({ promptLoading: loading }),
    
    // API操作
    sendChatMessage: async (data) => {
      set({ chatLoading: true, error: null, currentQuestion: data.question });
      try {
        const result = await qwenApi.chat(data);
        set((state) => ({
          ...state,
          chatHistory: [...state.chatHistory, result],
          chatLoading: false,
          currentQuestion: ''
        }));
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '发送失败';
        set({ error: errorMessage, chatLoading: false, currentQuestion: '' });
      }
    },
    
    generateArticle: async (data) => {
      set({ articleLoading: true, error: null });
      try {
        const result = await qwenApi.generateArticle(data);
        set({ generatedArticle: result, articleLoading: false });
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '生成失败';
        set({ error: errorMessage, articleLoading: false });
      }
    },
    
    optimizePrompt: async (input) => {
      set({ promptLoading: true, error: null });
      try {
        const result = await qwenApi.optimizePrompt({ input });
        set({ optimizedPrompt: result.optimizedPrompt, promptLoading: false });
        return result.optimizedPrompt;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '优化失败';
        set({ error: errorMessage, promptLoading: false });
        throw error;
      }
    },
    
    clearChatHistory: () => set({ chatHistory: [] }),
  }));

// 便捷hooks
export const useImageGeneration = () => {
  const store = useImageStore();
  
  const createImage = async (prompt: string, options?: Partial<ImageGenerationRequest>) => {
    const data: ImageGenerationRequest = {
      prompt,
      ...store.formData,
      ...options,
    };
    return store.createTextToImage(data);
  };
  
  return {
    ...store,
    createImage,
  };
};

export const useQwenChat = () => {
  const store = useQwenStore();
  
  const askQuestion = async (question: string, context?: string) => {
    return store.sendChatMessage({ question, context });
  };
  
  return {
    ...store,
    askQuestion,
  };
};
