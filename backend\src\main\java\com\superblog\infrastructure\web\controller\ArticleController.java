package com.superblog.infrastructure.web.controller;

import io.vertx.ext.web.RoutingContext;

/**
 * 文章控制器
 * TODO: 待实现
 */
public class ArticleController {
    
    public void getArticles(RoutingContext context) {
        // TODO: 实现获取文章列表
        context.response()
            .putHeader("Content-Type", "application/json")
            .end("{\"message\":\"文章功能开发中\"}");
    }

    public void getArticleById(RoutingContext context) {
        // TODO: 实现获取文章详情
        context.response()
            .putHeader("Content-Type", "application/json")
            .end("{\"message\":\"文章详情功能开发中\"}");
    }
}
