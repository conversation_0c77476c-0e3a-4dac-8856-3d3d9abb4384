package com.superblog.config;

import com.google.inject.AbstractModule;
import com.google.inject.Singleton;
import com.superblog.application.service.ImageGenerationService;
import com.superblog.application.service.VideoService;
import com.superblog.application.service.ArticleService;
import com.superblog.application.service.ProjectService;
import com.superblog.application.service.UserService;
import com.superblog.domain.repository.ImageGenerationRepository;
import com.superblog.domain.repository.VideoRepository;
import com.superblog.domain.repository.ArticleRepository;
import com.superblog.domain.repository.ProjectRepository;
import com.superblog.domain.repository.UserRepository;
import com.superblog.infrastructure.repository.ImageGenerationRepositoryImpl;
import com.superblog.infrastructure.repository.VideoRepositoryImpl;
import com.superblog.infrastructure.repository.ArticleRepositoryImpl;
import com.superblog.infrastructure.repository.ProjectRepositoryImpl;
import com.superblog.infrastructure.repository.UserRepositoryImpl;
import com.superblog.infrastructure.web.RouterFactory;
import com.superblog.infrastructure.web.controller.ImageGenerationController;
import com.superblog.infrastructure.web.controller.VideoController;
import com.superblog.infrastructure.web.controller.ArticleController;
import com.superblog.infrastructure.web.controller.ProjectController;
import com.superblog.infrastructure.web.controller.UserController;
import com.superblog.infrastructure.external.ai.OpenAiClient;
import com.superblog.infrastructure.external.ai.StabilityAiClient;

/**
 * 服务模块
 * 负责提供业务服务相关的依赖注入
 */
public class ServiceModule extends AbstractModule {

    @Override
    protected void configure() {
        // 绑定Repository实现
        bind(ImageGenerationRepository.class).to(ImageGenerationRepositoryImpl.class).in(Singleton.class);
        bind(VideoRepository.class).to(VideoRepositoryImpl.class).in(Singleton.class);
        bind(ArticleRepository.class).to(ArticleRepositoryImpl.class).in(Singleton.class);
        bind(ProjectRepository.class).to(ProjectRepositoryImpl.class).in(Singleton.class);
        bind(UserRepository.class).to(UserRepositoryImpl.class).in(Singleton.class);

        // 绑定Service实现
        bind(ImageGenerationService.class).in(Singleton.class);
        bind(VideoService.class).in(Singleton.class);
        bind(ArticleService.class).in(Singleton.class);
        bind(ProjectService.class).in(Singleton.class);
        bind(UserService.class).in(Singleton.class);

        // 绑定Controller
        bind(ImageGenerationController.class).in(Singleton.class);
        bind(VideoController.class).in(Singleton.class);
        bind(ArticleController.class).in(Singleton.class);
        bind(ProjectController.class).in(Singleton.class);
        bind(UserController.class).in(Singleton.class);

        // 绑定外部服务客户端
        bind(OpenAiClient.class).in(Singleton.class);
        bind(StabilityAiClient.class).in(Singleton.class);

        // 绑定Web相关组件
        bind(RouterFactory.class).in(Singleton.class);
    }
}
