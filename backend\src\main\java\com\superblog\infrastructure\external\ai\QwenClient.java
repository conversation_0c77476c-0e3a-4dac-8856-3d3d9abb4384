package com.superblog.infrastructure.external.ai;

import com.google.inject.Inject;
import com.superblog.config.AiConfig;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.Vertx;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.client.HttpResponse;
import io.vertx.ext.web.client.WebClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 通义千问AI客户端
 * 集成阿里云通义千问大模型API
 */
public class QwenClient {

    private static final Logger logger = LoggerFactory.getLogger(QwenClient.class);
    
    private final WebClient webClient;
    private final AiConfig.QwenConfig qwenConfig;

    @Inject
    public QwenClient(Vertx vertx, AiConfig aiConfig) {
        this.webClient = WebClient.create(vertx);
        this.qwenConfig = aiConfig.qwen();
    }

    /**
     * 生成文本内容
     */
    public Future<String> generateText(String prompt) {
        return generateText(prompt, null, 1000, 0.7f);
    }

    /**
     * 生成文本内容（带参数）
     */
    public Future<String> generateText(String prompt, String systemMessage, int maxTokens, float temperature) {
        Promise<String> promise = Promise.promise();

        if (qwenConfig.apiKey() == null || qwenConfig.apiKey().isEmpty()) {
            promise.fail(new IllegalStateException("通义千问API密钥未配置"));
            return promise.future();
        }

        logger.info("正在调用通义千问API生成文本...");

        // 构建请求体
        JsonObject requestBody = new JsonObject()
            .put("model", qwenConfig.model())
            .put("input", new JsonObject()
                .put("messages", buildMessages(prompt, systemMessage)))
            .put("parameters", new JsonObject()
                .put("max_tokens", maxTokens)
                .put("temperature", temperature)
                .put("top_p", 0.8)
                .put("repetition_penalty", 1.1)
                .put("result_format", "message"));

        // 发送请求
        webClient.postAbs(qwenConfig.baseUrl() + "/services/aigc/text-generation/generation")
            .putHeader("Authorization", "Bearer " + qwenConfig.apiKey())
            .putHeader("Content-Type", "application/json")
            .putHeader("X-DashScope-SSE", "disable")
            .timeout(qwenConfig.timeout())
            .sendJsonObject(requestBody)
            .onSuccess(response -> handleTextResponse(response, promise))
            .onFailure(throwable -> {
                logger.error("通义千问API调用失败", throwable);
                promise.fail(throwable);
            });

        return promise.future();
    }

    /**
     * 生成图片描述（用于AI绘画的提示词优化）
     */
    public Future<String> generateImagePrompt(String userInput) {
        String systemMessage = "你是一个专业的AI绘画提示词专家。请将用户的描述转换为详细的英文绘画提示词，" +
            "包含画面内容、艺术风格、光线效果、色彩搭配等元素。要求：" +
            "1. 使用英文输出" +
            "2. 描述要具体生动" +
            "3. 包含艺术风格关键词" +
            "4. 适合AI绘画模型理解";

        return generateText(userInput, systemMessage, 500, 0.8f);
    }

    /**
     * 生成博客文章
     */
    public Future<String> generateBlogArticle(String topic, String outline) {
        String systemMessage = "你是一个专业的技术博客作者。请根据给定的主题和大纲，" +
            "生成一篇高质量的技术博客文章。要求：" +
            "1. 使用Markdown格式" +
            "2. 内容专业准确" +
            "3. 结构清晰" +
            "4. 包含代码示例（如适用）" +
            "5. 字数在1000-2000字之间";

        String prompt = String.format("主题：%s\n大纲：%s", topic, outline);
        return generateText(prompt, systemMessage, 2000, 0.7f);
    }

    /**
     * 智能问答
     */
    public Future<String> chat(String question, String context) {
        String systemMessage = "你是SuperBlog平台的智能助手，专门帮助用户解决技术问题和创作需求。" +
            "请提供准确、有用的回答。如果涉及代码，请提供具体的示例。";

        String prompt = context != null ? 
            String.format("上下文：%s\n问题：%s", context, question) : question;

        return generateText(prompt, systemMessage, 1000, 0.7f);
    }

    /**
     * 构建消息数组
     */
    private JsonArray buildMessages(String prompt, String systemMessage) {
        JsonArray messages = new JsonArray();

        if (systemMessage != null && !systemMessage.isEmpty()) {
            messages.add(new JsonObject()
                .put("role", "system")
                .put("content", systemMessage));
        }

        messages.add(new JsonObject()
            .put("role", "user")
            .put("content", prompt));

        return messages;
    }

    /**
     * 处理文本生成响应
     */
    private void handleTextResponse(HttpResponse<Buffer> response, Promise<String> promise) {
        try {
            if (response.statusCode() == 200) {
                JsonObject responseBody = response.bodyAsJsonObject();
                
                if (responseBody.containsKey("output")) {
                    JsonObject output = responseBody.getJsonObject("output");
                    if (output.containsKey("choices")) {
                        JsonArray choices = output.getJsonArray("choices");
                        if (choices.size() > 0) {
                            JsonObject firstChoice = choices.getJsonObject(0);
                            JsonObject message = firstChoice.getJsonObject("message");
                            String content = message.getString("content");
                            
                            logger.info("通义千问API调用成功，生成内容长度: {}", content.length());
                            promise.complete(content);
                            return;
                        }
                    }
                }
                
                logger.error("通义千问API响应格式异常: {}", responseBody.encode());
                promise.fail(new RuntimeException("API响应格式异常"));
            } else {
                String errorMessage = String.format("通义千问API调用失败，状态码: %d, 响应: %s", 
                    response.statusCode(), response.bodyAsString());
                logger.error(errorMessage);
                promise.fail(new RuntimeException(errorMessage));
            }
        } catch (Exception e) {
            logger.error("处理通义千问API响应时发生错误", e);
            promise.fail(e);
        }
    }

    /**
     * 测试API连接
     */
    public Future<Boolean> testConnection() {
        Promise<Boolean> promise = Promise.promise();

        generateText("你好，请回复'连接成功'")
            .onSuccess(response -> {
                logger.info("通义千问API连接测试成功");
                promise.complete(true);
            })
            .onFailure(throwable -> {
                logger.error("通义千问API连接测试失败", throwable);
                promise.complete(false);
            });

        return promise.future();
    }
}
