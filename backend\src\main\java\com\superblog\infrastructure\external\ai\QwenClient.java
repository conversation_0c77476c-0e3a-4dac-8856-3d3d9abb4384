package com.superblog.infrastructure.external.ai;

import com.google.inject.Inject;
import com.superblog.config.AiConfig;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.Vertx;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.client.HttpResponse;
import io.vertx.ext.web.client.WebClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 通义千问AI客户端
 * 集成阿里云通义千问大模型API
 */
public class QwenClient {

    private static final Logger logger = LoggerFactory.getLogger(QwenClient.class);
    
    private final Vertx vertx;
    private final WebClient webClient;
    private final AiConfig.QwenConfig qwenConfig;

    @Inject
    public QwenClient(Vertx vertx, AiConfig aiConfig) {
        this.vertx = vertx;
        this.webClient = WebClient.create(vertx);
        this.qwenConfig = aiConfig.qwen();
    }

    /**
     * 生成文本内容
     */
    public Future<String> generateText(String prompt) {
        return generateText(prompt, null, 1000, 0.7f);
    }

    /**
     * 生成文本内容（带参数）
     */
    public Future<String> generateText(String prompt, String systemMessage, int maxTokens, float temperature) {
        Promise<String> promise = Promise.promise();

        if (qwenConfig.apiKey() == null || qwenConfig.apiKey().isEmpty()) {
            promise.fail(new IllegalStateException("通义千问API密钥未配置"));
            return promise.future();
        }

        logger.info("正在调用通义千问API生成文本...");

        // 构建请求体
        JsonObject requestBody = new JsonObject()
            .put("model", qwenConfig.model())
            .put("input", new JsonObject()
                .put("messages", buildMessages(prompt, systemMessage)))
            .put("parameters", new JsonObject()
                .put("max_tokens", maxTokens)
                .put("temperature", temperature)
                .put("top_p", 0.8)
                .put("repetition_penalty", 1.1)
                .put("result_format", "message"));

        // 发送请求
        webClient.postAbs(qwenConfig.baseUrl() + "/services/aigc/text-generation/generation")
            .putHeader("Authorization", "Bearer " + qwenConfig.apiKey())
            .putHeader("Content-Type", "application/json")
            .putHeader("X-DashScope-SSE", "disable")
            .timeout(qwenConfig.timeout())
            .sendJsonObject(requestBody)
            .onSuccess(response -> handleTextResponse(response, promise))
            .onFailure(throwable -> {
                logger.error("通义千问API调用失败", throwable);
                promise.fail(throwable);
            });

        return promise.future();
    }

    /**
     * 生成图片描述（用于AI绘画的提示词优化）
     */
    public Future<String> generateImagePrompt(String userInput) {
        String systemMessage = "你是一个专业的AI绘画提示词专家。请将用户的描述转换为详细的英文绘画提示词，" +
            "包含画面内容、艺术风格、光线效果、色彩搭配等元素。要求：" +
            "1. 使用英文输出" +
            "2. 描述要具体生动" +
            "3. 包含艺术风格关键词" +
            "4. 适合AI绘画模型理解";

        return generateText(userInput, systemMessage, 500, 0.8f);
    }

    /**
     * 生成博客文章
     */
    public Future<String> generateBlogArticle(String topic, String outline) {
        String systemMessage = "你是一个专业的技术博客作者。请根据给定的主题和大纲，" +
            "生成一篇高质量的技术博客文章。要求：" +
            "1. 使用Markdown格式" +
            "2. 内容专业准确" +
            "3. 结构清晰" +
            "4. 包含代码示例（如适用）" +
            "5. 字数在1000-2000字之间";

        String prompt = String.format("主题：%s\n大纲：%s", topic, outline);
        return generateText(prompt, systemMessage, 2000, 0.7f);
    }

    /**
     * 智能问答
     */
    public Future<String> chat(String question, String context) {
        String systemMessage = "你是SuperBlog平台的智能助手，专门帮助用户解决技术问题和创作需求。" +
            "请提供准确、有用的回答。如果涉及代码，请提供具体的示例。";

        String prompt = context != null ? 
            String.format("上下文：%s\n问题：%s", context, question) : question;

        return generateText(prompt, systemMessage, 1000, 0.7f);
    }

    /**
     * 生成图片（使用万相模型）
     */
    public Future<String> generateImage(com.superblog.domain.model.ImageGeneration generation) {
        Promise<String> promise = Promise.promise();

        if (qwenConfig.apiKey() == null || qwenConfig.apiKey().isEmpty()) {
            logger.warn("通义千问API密钥未配置，返回mock图片URL");
            // 返回mock图片URL
            try {
                String mockImageUrl = "https://via.placeholder.com/" + generation.getWidth() + "x" + generation.getHeight() + 
                    "/4a90e2/ffffff?text=" + java.net.URLEncoder.encode("通义千问生成：" + generation.getPrompt().substring(0, Math.min(10, generation.getPrompt().length())), "UTF-8");
                promise.complete(mockImageUrl);
            } catch (Exception e) {
                String simpleMockUrl = "https://via.placeholder.com/" + generation.getWidth() + "x" + generation.getHeight() + "/4a90e2/ffffff?text=Qwen+Generated";
                promise.complete(simpleMockUrl);
            }
            return promise.future();
        }

        try {
            // 构建请求体
            JsonObject requestBody = buildImageGenerationRequest(generation);
            
            logger.info("调用通义千问万相模型生成图片，prompt: {}", generation.getPrompt());

            // 发送请求
            webClient
                .postAbs("https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis")
                .putHeader("Authorization", "Bearer " + qwenConfig.apiKey())
                .putHeader("Content-Type", "application/json")
                .putHeader("X-DashScope-Async", "enable") // 启用异步模式
                .sendJsonObject(requestBody)
                .onSuccess(response -> handleImageGenerationResponse(response, promise))
                .onFailure(throwable -> {
                    logger.error("通义千问图片生成API调用失败", throwable);
                    promise.fail(throwable);
                });

        } catch (Exception e) {
            logger.error("构建通义千问图片生成请求失败", e);
            promise.fail(e);
        }

        return promise.future();
    }

    /**
     * 构建图片生成请求
     */
    private JsonObject buildImageGenerationRequest(com.superblog.domain.model.ImageGeneration generation) {
        JsonObject input = new JsonObject()
            .put("prompt", generation.getPrompt())
            .put("style", mapStyleToQwen(generation.getStyle()))
            .put("size", generation.getWidth() + "*" + generation.getHeight())
            .put("n", 1); // 生成图片数量

        // 添加负面提示词
        if (generation.getNegativePrompt() != null && !generation.getNegativePrompt().isEmpty()) {
            input.put("negative_prompt", generation.getNegativePrompt());
        }

        // 添加种子值
        if (generation.getSeed() != null) {
            input.put("seed", generation.getSeed());
        }

        JsonObject parameters = new JsonObject()
            .put("style", mapStyleToQwen(generation.getStyle()));

        return new JsonObject()
            .put("model", "wanx-v1") // 使用万相模型
            .put("input", input)
            .put("parameters", parameters);
    }

    /**
     * 将通用风格映射到通义千问万相模型的风格
     */
    private String mapStyleToQwen(String style) {
        if (style == null) return "<auto>";
        
        switch (style) {
            case "写实风格":
                return "<photography>";
            case "动漫风格":
                return "<anime>";
            case "油画风格":
                return "<oil painting>";
            case "水彩风格":
                return "<watercolor>";
            case "素描风格":
                return "<sketch>";
            case "科幻风格":
                return "<sci-fi>";
            default:
                return "<auto>";
        }
    }

    /**
     * 处理文本生成响应
     */
    private void handleTextResponse(HttpResponse<Buffer> response, Promise<String> promise) {
        try {
            if (response.statusCode() == 200) {
                JsonObject responseBody = response.bodyAsJsonObject();
                
                if (responseBody.containsKey("output")) {
                    JsonObject output = responseBody.getJsonObject("output");
                    if (output.containsKey("choices")) {
                        JsonArray choices = output.getJsonArray("choices");
                        if (choices.size() > 0) {
                            JsonObject firstChoice = choices.getJsonObject(0);
                            JsonObject message = firstChoice.getJsonObject("message");
                            String content = message.getString("content");
                            
                            logger.info("通义千问API调用成功，生成内容长度: {}", content.length());
                            promise.complete(content);
                            return;
                        }
                    }
                }
                
                logger.error("通义千问API响应格式异常: {}", responseBody.encode());
                promise.fail(new RuntimeException("API响应格式异常"));
            } else {
                String errorMessage = String.format("通义千问API调用失败，状态码: %d, 响应: %s", 
                    response.statusCode(), response.bodyAsString());
                logger.error(errorMessage);
                promise.fail(new RuntimeException(errorMessage));
            }
        } catch (Exception e) {
            logger.error("处理通义千问API响应时发生错误", e);
            promise.fail(e);
        }
    }

    /**
     * 处理图片生成响应
     */
    private void handleImageGenerationResponse(HttpResponse<Buffer> response, Promise<String> promise) {
        try {
            if (response.statusCode() == 200) {
                JsonObject responseBody = response.bodyAsJsonObject();
                
                // 检查是否是异步任务
                if (responseBody.containsKey("output")) {
                    JsonObject output = responseBody.getJsonObject("output");
                    
                    if (output.containsKey("task_id")) {
                        // 异步任务，需要轮询结果
                        String taskId = output.getString("task_id");
                        logger.info("通义千问图片生成任务已提交，任务ID: {}", taskId);
                        pollImageGenerationResult(taskId, promise);
                    } else if (output.containsKey("results")) {
                        // 同步响应，直接获取结果
                        JsonArray results = output.getJsonArray("results");
                        if (results.size() > 0) {
                            JsonObject firstResult = results.getJsonObject(0);
                            String imageUrl = firstResult.getString("url");
                            logger.info("通义千问图片生成成功，图片URL: {}", imageUrl);
                            promise.complete(imageUrl);
                        } else {
                            promise.fail(new RuntimeException("通义千问API返回空结果"));
                        }
                    }
                } else {
                    logger.error("通义千问图片生成API响应格式异常: {}", responseBody.encode());
                    promise.fail(new RuntimeException("API响应格式异常"));
                }
            } else {
                String errorMessage = String.format("通义千问图片生成API调用失败，状态码: %d, 响应: %s", 
                    response.statusCode(), response.bodyAsString());
                logger.error(errorMessage);
                promise.fail(new RuntimeException(errorMessage));
            }
        } catch (Exception e) {
            logger.error("处理通义千问图片生成API响应时发生错误", e);
            promise.fail(e);
        }
    }

    /**
     * 轮询图片生成结果
     */
    private void pollImageGenerationResult(String taskId, Promise<String> promise) {
        pollImageGenerationResult(taskId, promise, 0);
    }

    /**
     * 轮询图片生成结果（带重试计数）
     */
    private void pollImageGenerationResult(String taskId, Promise<String> promise, int retryCount) {
        if (retryCount >= 30) { // 最多轮询30次（约5分钟）
            promise.fail(new RuntimeException("通义千问图片生成超时"));
            return;
        }

        webClient
            .getAbs("https://dashscope.aliyuncs.com/api/v1/tasks/" + taskId)
            .putHeader("Authorization", "Bearer " + qwenConfig.apiKey())
            .send()
            .onSuccess(response -> {
                try {
                    if (response.statusCode() == 200) {
                        JsonObject responseBody = response.bodyAsJsonObject();
                        JsonObject output = responseBody.getJsonObject("output");
                        String taskStatus = output.getString("task_status");

                        switch (taskStatus) {
                            case "SUCCEEDED":
                                JsonArray results = output.getJsonArray("results");
                                if (results.size() > 0) {
                                    JsonObject firstResult = results.getJsonObject(0);
                                    String imageUrl = firstResult.getString("url");
                                    logger.info("通义千问图片生成完成，图片URL: {}", imageUrl);
                                    promise.complete(imageUrl);
                                } else {
                                    promise.fail(new RuntimeException("通义千问API返回空结果"));
                                }
                                break;
                            case "FAILED":
                                String errorCode = output.getString("code", "UNKNOWN");
                                String errorMessage = output.getString("message", "图片生成失败");
                                logger.error("通义千问图片生成失败: {} - {}", errorCode, errorMessage);
                                promise.fail(new RuntimeException("图片生成失败: " + errorMessage));
                                break;
                            case "PENDING":
                            case "RUNNING":
                                // 继续轮询
                                vertx.setTimer(10000, id -> { // 10秒后重试
                                    pollImageGenerationResult(taskId, promise, retryCount + 1);
                                });
                                break;
                            default:
                                logger.warn("通义千问图片生成任务状态未知: {}", taskStatus);
                                vertx.setTimer(10000, id -> { // 10秒后重试
                                    pollImageGenerationResult(taskId, promise, retryCount + 1);
                                });
                                break;
                        }
                    } else {
                        logger.error("查询通义千问图片生成任务状态失败，状态码: {}", response.statusCode());
                        promise.fail(new RuntimeException("查询任务状态失败"));
                    }
                } catch (Exception e) {
                    logger.error("处理通义千问图片生成任务状态响应时发生错误", e);
                    promise.fail(e);
                }
            })
            .onFailure(throwable -> {
                logger.error("查询通义千问图片生成任务状态失败", throwable);
                promise.fail(throwable);
            });
    }

    /**
     * 测试API连接
     */
    public Future<Boolean> testConnection() {
        Promise<Boolean> promise = Promise.promise();

        generateText("你好，请回复'连接成功'")
            .onSuccess(response -> {
                logger.info("通义千问API连接测试成功");
                promise.complete(true);
            })
            .onFailure(throwable -> {
                logger.error("通义千问API连接测试失败", throwable);
                promise.complete(false);
            });

        return promise.future();
    }

    /**
     * 构建消息数组
     */
    private JsonArray buildMessages(String prompt, String systemMessage) {
        JsonArray messages = new JsonArray();
        
        // 添加系统消息
        if (systemMessage != null && !systemMessage.isEmpty()) {
            messages.add(new JsonObject()
                .put("role", "system")
                .put("content", systemMessage));
        }
        
        // 添加用户消息
        messages.add(new JsonObject()
            .put("role", "user")
            .put("content", prompt));
            
        return messages;
    }
}
