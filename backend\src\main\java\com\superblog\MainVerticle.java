package com.superblog;

import com.google.inject.Guice;
import com.google.inject.Injector;
import com.superblog.config.ConfigModule;
import com.superblog.config.DatabaseModule;
import com.superblog.config.ServiceModule;
import com.superblog.infrastructure.database.DatabaseMigration;
import com.superblog.infrastructure.web.RouterFactory;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.Vertx;
import io.vertx.core.http.HttpServer;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * SuperBlog 主启动类
 * 负责初始化应用程序的各个组件和服务
 */
public class MainVerticle extends AbstractVerticle {

    private static final Logger logger = LoggerFactory.getLogger(MainVerticle.class);

    private Injector injector;
    private HttpServer server;

    @Override
    public void start(Promise<Void> startPromise) throws Exception {
        logger.info("正在启动 SuperBlog 应用程序...");

        try {
            // 初始化依赖注入容器
            initializeInjector()
                    .compose(v -> runDatabaseMigration())
                    .compose(v -> createHttpServer())
                    .onSuccess(v -> {
                        logger.info("SuperBlog 应用程序启动成功，端口: {}", config().getInteger("server.port", 8080));
                        startPromise.complete();
                    })
                    .onFailure(throwable -> {
                        logger.error("SuperBlog 应用程序启动失败", throwable);
                        startPromise.fail(throwable);
                    });
        } catch (Exception e) {
            logger.error("初始化应用程序时发生错误", e);
            startPromise.fail(e);
        }
    }

    @Override
    public void stop(Promise<Void> stopPromise) throws Exception {
        logger.info("正在停止 SuperBlog 应用程序...");

        if (server != null) {
            server.close()
                    .onSuccess(v -> {
                        logger.info("HTTP 服务器已停止");
                        stopPromise.complete();
                    })
                    .onFailure(throwable -> {
                        logger.error("停止 HTTP 服务器时发生错误", throwable);
                        stopPromise.fail(throwable);
                    });
        } else {
            stopPromise.complete();
        }
    }

    /**
     * 初始化依赖注入容器
     */
    private Future<Void> initializeInjector() {
        Promise<Void> promise = Promise.promise();

        try {
            JsonObject config = config();

            injector = Guice.createInjector(
                    new ConfigModule(vertx, config),
                    new DatabaseModule(),
                    new ServiceModule()
            );

            logger.info("依赖注入容器初始化完成");
            promise.complete();
        } catch (Exception e) {
            logger.error("初始化依赖注入容器失败", e);
            promise.fail(e);
        }

        return promise.future();
    }

    /**
     * 运行数据库迁移
     */
    private Future<Void> runDatabaseMigration() {
        Promise<Void> promise = Promise.promise();

        try {
            DatabaseMigration migration = injector.getInstance(DatabaseMigration.class);
            migration.migrate()
                    .onSuccess(v -> {
                        logger.info("数据库迁移完成");
                        promise.complete();
                    })
                    .onFailure(throwable -> {
                        logger.error("数据库迁移失败", throwable);
                        promise.fail(throwable);
                    });
        } catch (Exception e) {
            logger.error("执行数据库迁移时发生错误", e);
            promise.fail(e);
        }

        return promise.future();
    }

    /**
     * 创建HTTP服务器
     */
    private Future<Void> createHttpServer() {
        Promise<Void> promise = Promise.promise();

        try {
            RouterFactory routerFactory = injector.getInstance(RouterFactory.class);
            Router router = routerFactory.createRouter();

            JsonObject serverConfig = config().getJsonObject("server", new JsonObject());
            int port = serverConfig.getInteger("port", 8080);
            String host = serverConfig.getString("host", "0.0.0.0");

            server = vertx.createHttpServer();
            server.requestHandler(router)
                    .listen(port, host)
                    .onSuccess(httpServer -> {
                        logger.info("HTTP 服务器启动成功，监听地址: {}:{}", host, port);
                        promise.complete();
                    })
                    .onFailure(throwable -> {
                        logger.error("HTTP 服务器启动失败", throwable);
                        promise.fail(throwable);
                    });
        } catch (Exception e) {
            logger.error("创建 HTTP 服务器时发生错误", e);
            promise.fail(e);
        }

        return promise.future();
    }

    /**
     * 主入口方法
     * 用于在IDE中直接运行
     */
    public static void main(String[] args) {
        System.setProperty("vertx.logger-delegate-factory-class-name",
            "io.vertx.core.logging.SLF4JLogDelegateFactory");

        // 创建Vertx实例
        Vertx vertx = Vertx.vertx();

        // 加载配置
        JsonObject config = loadConfiguration();

        // 部署Verticle
        vertx.deployVerticle(new MainVerticle(),
            new io.vertx.core.DeploymentOptions().setConfig(config))
            .onSuccess(id -> {
                System.out.println("✅ SuperBlog 启动成功！");
                System.out.println("🌐 访问地址: http://localhost:" + config.getInteger("server.port", 8080));
                System.out.println("❤️ 健康检查: http://localhost:" + config.getInteger("server.port", 8080) + "/api/health");
            })
            .onFailure(throwable -> {
                System.err.println("❌ SuperBlog 启动失败: " + throwable.getMessage());
                throwable.printStackTrace();
                System.exit(1);
            });
    }

    /**
     * 加载配置文件
     */
    private static JsonObject loadConfiguration() {
        try {
            // 读取配置文件
            String configFile = "application.json";
            String profile = System.getProperty("profile", System.getenv("PROFILE"));

            if (profile != null && !profile.isEmpty()) {
                configFile = "application-" + profile + ".json";
                System.out.println("📋 使用配置文件: " + configFile);
            }

            // 从classpath读取配置
            var inputStream = MainVerticle.class.getClassLoader()
                .getResourceAsStream(configFile);

            if (inputStream == null) {
                System.out.println("⚠️ 配置文件 " + configFile + " 不存在，使用默认配置");
                return getDefaultConfig();
            }

            String configContent = new String(inputStream.readAllBytes());
            JsonObject config = new JsonObject(configContent);

            // 替换环境变量
            replaceEnvironmentVariables(config);

            return config;
        } catch (Exception e) {
            System.err.println("❌ 加载配置文件失败: " + e.getMessage());
            return getDefaultConfig();
        }
    }

    /**
     * 获取默认配置
     */
    private static JsonObject getDefaultConfig() {
        return new JsonObject()
            .put("server", new JsonObject()
                .put("port", 8080)
                .put("host", "0.0.0.0"))
            .put("database", new JsonObject()
                .put("host", "localhost")
                .put("port", 5432)
                .put("database", "superblog_dev")
                .put("username", "superblog")
                .put("password", "superblog123")
                .put("maxPoolSize", 10))
            .put("redis", new JsonObject()
                .put("host", "localhost")
                .put("port", 6379)
                .put("database", 1)
                .put("maxPoolSize", 5))
            .put("cors", new JsonObject()
                .put("allowedOrigins", new io.vertx.core.json.JsonArray().add("*"))
                .put("allowedMethods", new io.vertx.core.json.JsonArray()
                    .add("GET").add("POST").add("PUT").add("DELETE").add("OPTIONS"))
                .put("allowedHeaders", new io.vertx.core.json.JsonArray()
                    .add("Content-Type").add("Authorization").add("X-Requested-With"))
                .put("allowCredentials", true));
    }

    /**
     * 替换配置中的环境变量
     */
    private static void replaceEnvironmentVariables(JsonObject config) {
        // 递归替换所有字符串值中的环境变量
        replaceEnvInJsonObject(config);
    }

    private static void replaceEnvInJsonObject(JsonObject obj) {
        for (String key : obj.fieldNames()) {
            Object value = obj.getValue(key);
            if (value instanceof String) {
                String strValue = (String) value;
                if (strValue.startsWith("${") && strValue.endsWith("}")) {
                    String envVar = strValue.substring(2, strValue.length() - 1);
                    String envValue = System.getenv(envVar);
                    if (envValue != null) {
                        obj.put(key, envValue);
                    }
                }
            } else if (value instanceof JsonObject) {
                replaceEnvInJsonObject((JsonObject) value);
            }
        }
    }
}
