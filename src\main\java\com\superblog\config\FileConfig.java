package com.superblog.config;

import java.util.List;

/**
 * 文件配置类
 */
public record FileConfig(
    String uploadPath,
    Long maxFileSize,
    List<String> allowedTypes
) {
    
    /**
     * 检查文件类型是否允许
     */
    public boolean isAllowedType(String contentType) {
        return allowedTypes.contains(contentType);
    }
    
    /**
     * 检查文件大小是否允许
     */
    public boolean isAllowedSize(long fileSize) {
        return fileSize <= maxFileSize;
    }
}
