/**
 * 主布局组件
 * 包含导航栏、侧边栏和主内容区域
 */

import React from 'react';
import { Outlet } from 'react-router-dom';
import Header from './Header';
import Sidebar from './Sidebar';
import { useAppStore } from '@/stores/appStore';

const Layout: React.FC = () => {
  const { sidebarOpen, mobileMenuOpen } = useAppStore();

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <Header />
      
      <div className="flex">
        {/* Sidebar */}
        <Sidebar />
        
        {/* Main Content */}
        <main className={`
          flex-1 transition-all duration-300 ease-in-out
          ${sidebarOpen ? 'lg:ml-64' : 'lg:ml-16'}
          pt-16
        `}>
          <div className="min-h-screen">
            <Outlet />
          </div>
        </main>
      </div>
      
      {/* Mobile Menu Overlay */}
      {mobileMenuOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => useAppStore.getState().toggleMobileMenu()}
        />
      )}
    </div>
  );
};

export default Layout;
