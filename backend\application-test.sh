#!/bin/bash

# Application.java 测试脚本

echo "=== Application.java 启动测试 ==="

BASE_URL="http://localhost:8080/api"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试函数
test_api() {
    local name="$1"
    local method="$2"
    local url="$3"
    local data="$4"
    
    echo -e "\n${YELLOW}测试: $name${NC}"
    echo "请求: $method $url"
    
    if [ -n "$data" ]; then
        echo "数据: $data"
        response=$(curl -s -w "\n%{http_code}" -X "$method" \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$url")
    else
        response=$(curl -s -w "\n%{http_code}" -X "$method" "$url")
    fi
    
    # 分离响应体和状态码
    body=$(echo "$response" | head -n -1)
    status=$(echo "$response" | tail -n 1)
    
    echo "状态码: $status"
    echo "响应: $body"
    
    if [ "$status" = "200" ]; then
        echo -e "${GREEN}✓ 测试通过${NC}"
    else
        echo -e "${RED}✗ 测试失败${NC}"
    fi
}

echo -e "${YELLOW}说明: 请先在IDE中运行 Application.java${NC}"
echo -e "${YELLOW}运行方式: 右键点击 Application.java -> Run 'Application.main()'${NC}"
echo ""

# 等待服务启动
echo -e "${YELLOW}等待服务启动...${NC}"
for i in {1..30}; do
    if curl -s "$BASE_URL/health" > /dev/null 2>&1; then
        echo -e "${GREEN}服务已启动${NC}"
        break
    fi
    echo -n "."
    sleep 1
done

echo ""

# 基础测试
test_api "健康检查" "GET" "$BASE_URL/health" ""

# AI功能测试
test_api "创建文生图任务" "POST" "$BASE_URL/ai/text-to-image" '{
    "prompt": "一只可爱的橘猫"
}'

test_api "通义千问问答" "POST" "$BASE_URL/ai/chat" '{
    "question": "你好，请介绍一下Java编程语言"
}'

test_api "生成博客文章" "POST" "$BASE_URL/ai/generate-article" '{
    "topic": "Java入门教程"
}'

test_api "优化绘画提示词" "POST" "$BASE_URL/ai/optimize-prompt" '{
    "input": "一只猫在花园里"
}'

test_api "获取图片生成记录" "GET" "$BASE_URL/ai/generations/public" ""

test_api "通义千问API测试" "GET" "$BASE_URL/ai/qwen/test" ""

echo -e "\n${GREEN}=== Application.java 测试完成 ===${NC}"

echo -e "\n${YELLOW}=== 启动方式对比 ===${NC}"
echo "1. MinimalApplication.java   - 最简版本，确保能启动"
echo "2. Application.java         - 完整版本，带配置和日志"
echo "3. SimpleApplication.java   - 中等复杂度版本"
echo "4. SimpleMainVerticle.java  - Verticle版本"
echo ""
echo "推荐使用顺序: MinimalApplication -> Application -> SimpleApplication"
