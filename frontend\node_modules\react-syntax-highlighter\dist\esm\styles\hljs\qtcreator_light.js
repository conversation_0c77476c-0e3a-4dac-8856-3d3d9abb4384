export default {
  "hljs": {
    "display": "block",
    "overflowX": "auto",
    "padding": "0.5em",
    "background": "#ffffff",
    "color": "#000000"
  },
  "hljs-subst": {
    "color": "#000000"
  },
  "hljs-tag": {
    "color": "#000000"
  },
  "hljs-title": {
    "color": "#000000"
  },
  "hljs-strong": {
    "color": "#000000"
  },
  "hljs-emphasis": {
    "color": "#000000",
    "fontStyle": "italic"
  },
  "hljs-bullet": {
    "color": "#000080"
  },
  "hljs-quote": {
    "color": "#000080"
  },
  "hljs-number": {
    "color": "#000080"
  },
  "hljs-regexp": {
    "color": "#000080"
  },
  "hljs-literal": {
    "color": "#000080"
  },
  "hljs-code\n.hljs-selector-class": {
    "color": "#800080"
  },
  "hljs-stronge": {
    "fontStyle": "italic"
  },
  "hljs-type": {
    "fontStyle": "italic",
    "color": "#008000"
  },
  "hljs-keyword": {
    "color": "#808000"
  },
  "hljs-selector-tag": {
    "color": "#808000"
  },
  "hljs-function": {
    "color": "#808000"
  },
  "hljs-section": {
    "color": "#808000"
  },
  "hljs-symbol": {
    "color": "#808000"
  },
  "hljs-name": {
    "color": "#808000"
  },
  "hljs-attribute": {
    "color": "#800000"
  },
  "hljs-variable": {
    "color": "#0055AF"
  },
  "hljs-params": {
    "color": "#0055AF"
  },
  "hljs-class .hljs-title": {
    "color": "#0055AF"
  },
  "hljs-string": {
    "color": "#008000"
  },
  "hljs-selector-id": {
    "color": "#008000"
  },
  "hljs-selector-attr": {
    "color": "#008000"
  },
  "hljs-selector-pseudo": {
    "color": "#008000"
  },
  "hljs-built_in": {
    "color": "#008000"
  },
  "hljs-builtin-name": {
    "color": "#008000"
  },
  "hljs-template-tag": {
    "color": "#008000"
  },
  "hljs-template-variable": {
    "color": "#008000"
  },
  "hljs-addition": {
    "color": "#008000"
  },
  "hljs-link": {
    "color": "#008000"
  },
  "hljs-comment": {
    "color": "#008000"
  },
  "hljs-meta": {
    "color": "#008000"
  },
  "hljs-deletion": {
    "color": "#008000"
  }
};