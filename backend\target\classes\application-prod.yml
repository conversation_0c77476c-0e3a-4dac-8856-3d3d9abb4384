server:
  port: 8080
  servlet:
    context-path: /api

# 线程池配置
thread:
  pool:
    executor:
      config:
        core-pool-size: 20
        max-pool-size: 50
        keep-alive-time: 5000
        block-queue-size: 5000
        policy: CallerRunsPolicy

# 数据库配置
spring:
  datasource:
    username: ${DB_USERNAME:superblog}
    password: ${DB_PASSWORD:superblog123}
    url: ${DB_URL:**************************************************************************************************************}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 10
      idle-timeout: 30000
      connection-timeout: 30000
      max-lifetime: 1800000
  
  # Redis配置
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    timeout: 5000
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: -1
  
  main:
    allow-bean-definition-overriding: true

# AI服务配置
ai:
  # OpenAI配置
  openai:
    base-url: ${OPENAI_BASE_URL:https://api.openai.com/v1}
    api-key: ${OPENAI_API_KEY}
    model: ${OPENAI_MODEL:gpt-4}
    timeout: 120000
    
  # 通义千问配置  
  qwen:
    base-url: ${QWEN_BASE_URL:https://dashscope.aliyuncs.com/api/v1}
    api-key: ${QWEN_API_KEY}
    model: ${QWEN_MODEL:qwen-max}
    timeout: 120000
    
  # Stability AI配置
  stability:
    base-url: ${STABILITY_BASE_URL:https://api.stability.ai/v1}
    api-key: ${STABILITY_API_KEY}
    timeout: 180000

# 文件存储配置
file:
  upload:
    path: ${FILE_UPLOAD_PATH:/var/superblog/uploads}
    max-size: 50MB
    allowed-types: jpg,jpeg,png,gif,webp,pdf,doc,docx
  
# JWT配置
jwt:
  secret: ${JWT_SECRET}
  expiration: 86400000 # 24小时

# CORS配置
cors:
  allowed-origins: ${CORS_ALLOWED_ORIGINS:https://yourdomain.com}
  allowed-methods: GET,POST,PUT,DELETE,OPTIONS
  allowed-headers: "*"
  max-age: 3600

# 日志配置
logging:
  level:
    root: warn
    com.superblog: info
  config: classpath:logback.xml
        datasource:
          driver-class-name: org.postgresql.Driver
          username: postgres
          password: postgres
          url: ********************************************************
          type: com.zaxxer.hikari.HikariDataSource
    openai:
      base-url: https://apis.itedus.cn
      api-key: sk-gU8CZ5ZjMhqoq7922fD7488857F44d38A***可以找小傅哥申请

# MyBatis 配置
mybatis:
  mapper-locations: classpath:/mybatis/mapper/*.xml
  config-location: classpath:/mybatis/config/mybatis-config.xml

# 日志
logging:
  level:
    root: info
  config: classpath:logback-spring.xml