package com.superblog;

import com.google.inject.Guice;
import com.google.inject.Injector;
import com.superblog.config.ConfigModule;
import com.superblog.config.DatabaseModule;
import com.superblog.config.ServiceModule;
import com.superblog.infrastructure.database.DatabaseMigration;
import com.superblog.infrastructure.web.RouterFactory;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.http.HttpServer;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * SuperBlog 主启动类
 * 负责初始化应用程序的各个组件和服务
 */
public class MainVerticle extends AbstractVerticle {

    private static final Logger logger = LoggerFactory.getLogger(MainVerticle.class);
    
    private Injector injector;
    private HttpServer server;

    @Override
    public void start(Promise<Void> startPromise) throws Exception {
        logger.info("正在启动 SuperBlog 应用程序...");

        try {
            // 初始化依赖注入容器
            initializeInjector()
                .compose(v -> runDatabaseMigration())
                .compose(v -> createHttpServer())
                .onSuccess(v -> {
                    logger.info("SuperBlog 应用程序启动成功，端口: {}", config().getInteger("server.port", 8080));
                    startPromise.complete();
                })
                .onFailure(throwable -> {
                    logger.error("SuperBlog 应用程序启动失败", throwable);
                    startPromise.fail(throwable);
                });
        } catch (Exception e) {
            logger.error("初始化应用程序时发生错误", e);
            startPromise.fail(e);
        }
    }

    @Override
    public void stop(Promise<Void> stopPromise) throws Exception {
        logger.info("正在停止 SuperBlog 应用程序...");
        
        if (server != null) {
            server.close()
                .onSuccess(v -> {
                    logger.info("HTTP 服务器已停止");
                    stopPromise.complete();
                })
                .onFailure(throwable -> {
                    logger.error("停止 HTTP 服务器时发生错误", throwable);
                    stopPromise.fail(throwable);
                });
        } else {
            stopPromise.complete();
        }
    }

    /**
     * 初始化依赖注入容器
     */
    private Promise<Void> initializeInjector() {
        Promise<Void> promise = Promise.promise();
        
        try {
            JsonObject config = config();
            
            injector = Guice.createInjector(
                new ConfigModule(vertx, config),
                new DatabaseModule(),
                new ServiceModule()
            );
            
            logger.info("依赖注入容器初始化完成");
            promise.complete();
        } catch (Exception e) {
            logger.error("初始化依赖注入容器失败", e);
            promise.fail(e);
        }
        
        return promise;
    }

    /**
     * 运行数据库迁移
     */
    private Promise<Void> runDatabaseMigration() {
        Promise<Void> promise = Promise.promise();
        
        try {
            DatabaseMigration migration = injector.getInstance(DatabaseMigration.class);
            migration.migrate()
                .onSuccess(v -> {
                    logger.info("数据库迁移完成");
                    promise.complete();
                })
                .onFailure(throwable -> {
                    logger.error("数据库迁移失败", throwable);
                    promise.fail(throwable);
                });
        } catch (Exception e) {
            logger.error("执行数据库迁移时发生错误", e);
            promise.fail(e);
        }
        
        return promise;
    }

    /**
     * 创建HTTP服务器
     */
    private Promise<Void> createHttpServer() {
        Promise<Void> promise = Promise.promise();
        
        try {
            RouterFactory routerFactory = injector.getInstance(RouterFactory.class);
            Router router = routerFactory.createRouter();
            
            JsonObject serverConfig = config().getJsonObject("server", new JsonObject());
            int port = serverConfig.getInteger("port", 8080);
            String host = serverConfig.getString("host", "0.0.0.0");
            
            server = vertx.createHttpServer();
            server.requestHandler(router)
                .listen(port, host)
                .onSuccess(httpServer -> {
                    logger.info("HTTP 服务器启动成功，监听地址: {}:{}", host, port);
                    promise.complete();
                })
                .onFailure(throwable -> {
                    logger.error("HTTP 服务器启动失败", throwable);
                    promise.fail(throwable);
                });
        } catch (Exception e) {
            logger.error("创建 HTTP 服务器时发生错误", e);
            promise.fail(e);
        }
        
        return promise;
    }
}
