#!/bin/bash

# SuperBlog 最简版测试脚本

echo "=== SuperBlog 最简版 API 测试 ==="

BASE_URL="http://localhost:8080/api"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试函数
test_api() {
    local name="$1"
    local method="$2"
    local url="$3"
    local data="$4"
    
    echo -e "\n${YELLOW}测试: $name${NC}"
    echo "请求: $method $url"
    
    if [ -n "$data" ]; then
        echo "数据: $data"
        response=$(curl -s -w "\n%{http_code}" -X "$method" \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$url")
    else
        response=$(curl -s -w "\n%{http_code}" -X "$method" "$url")
    fi
    
    # 分离响应体和状态码
    body=$(echo "$response" | head -n -1)
    status=$(echo "$response" | tail -n 1)
    
    echo "状态码: $status"
    echo "响应: $body"
    
    if [ "$status" = "200" ]; then
        echo -e "${GREEN}✓ 测试通过${NC}"
    else
        echo -e "${RED}✗ 测试失败${NC}"
    fi
}

# 等待服务启动
echo -e "${YELLOW}等待服务启动...${NC}"
for i in {1..30}; do
    if curl -s "$BASE_URL/health" > /dev/null 2>&1; then
        echo -e "${GREEN}服务已启动${NC}"
        break
    fi
    echo -n "."
    sleep 1
done

echo ""

# 基础测试
test_api "健康检查" "GET" "$BASE_URL/health" ""

# AI功能测试
test_api "创建文生图任务" "POST" "$BASE_URL/ai/text-to-image" '{
    "prompt": "一只可爱的橘猫"
}'

test_api "通义千问问答" "POST" "$BASE_URL/ai/chat" '{
    "question": "你好，请介绍一下Java编程语言"
}'

test_api "生成博客文章" "POST" "$BASE_URL/ai/generate-article" '{
    "topic": "Java入门教程"
}'

test_api "优化绘画提示词" "POST" "$BASE_URL/ai/optimize-prompt" '{
    "input": "一只猫在花园里"
}'

test_api "获取图片生成记录" "GET" "$BASE_URL/ai/generations/public" ""

test_api "通义千问API测试" "GET" "$BASE_URL/ai/qwen/test" ""

# 其他模块测试
test_api "获取视频列表" "GET" "$BASE_URL/videos" ""
test_api "获取文章列表" "GET" "$BASE_URL/articles" ""
test_api "获取项目列表" "GET" "$BASE_URL/projects" ""

# 错误测试
test_api "404错误测试" "GET" "$BASE_URL/nonexistent" ""

echo -e "\n${GREEN}=== 最简版测试完成 ===${NC}"

# 显示可用接口
echo -e "\n${YELLOW}=== 可用接口列表 ===${NC}"
echo "1. 健康检查: GET /api/health"
echo "2. AI文生图: POST /api/ai/text-to-image"
echo "3. 通义千问问答: POST /api/ai/chat"
echo "4. 生成博客文章: POST /api/ai/generate-article"
echo "5. 优化绘画提示词: POST /api/ai/optimize-prompt"
echo "6. 获取图片记录: GET /api/ai/generations/public"
echo "7. API连接测试: GET /api/ai/qwen/test"
echo "8. 视频列表: GET /api/videos"
echo "9. 文章列表: GET /api/articles"
echo "10. 项目列表: GET /api/projects"

echo -e "\n${YELLOW}通义千问API密钥: sk-4606dfde828a4f9aa7a43f5d53dddb9e${NC}"
