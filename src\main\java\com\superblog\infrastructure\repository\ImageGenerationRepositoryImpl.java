package com.superblog.infrastructure.repository;

import com.google.inject.Inject;
import com.superblog.domain.model.ImageGeneration;
import com.superblog.domain.repository.ImageGenerationRepository;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.pgclient.PgPool;
import io.vertx.sqlclient.Row;
import io.vertx.sqlclient.RowSet;
import io.vertx.sqlclient.Tuple;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * AI图片生成仓储实现类
 * 实现图片生成相关的数据访问操作
 */
public class ImageGenerationRepositoryImpl implements ImageGenerationRepository {

    private static final Logger logger = LoggerFactory.getLogger(ImageGenerationRepositoryImpl.class);

    private final PgPool pgPool;

    @Inject
    public ImageGenerationRepositoryImpl(PgPool pgPool) {
        this.pgPool = pgPool;
    }

    @Override
    public Future<ImageGeneration> save(ImageGeneration imageGeneration) {
        Promise<ImageGeneration> promise = Promise.promise();

        String sql = """
            INSERT INTO image_generations (
                user_id, generation_type, prompt, negative_prompt, original_image_url,
                width, height, style, model, seed, steps, cfg_scale, status, is_public,
                view_count, like_count, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18)
            RETURNING *
            """;

        Tuple params = Tuple.of(
            imageGeneration.getUserId(),
            imageGeneration.getGenerationType().name(),
            imageGeneration.getPrompt(),
            imageGeneration.getNegativePrompt(),
            imageGeneration.getOriginalImageUrl(),
            imageGeneration.getWidth(),
            imageGeneration.getHeight(),
            imageGeneration.getStyle(),
            imageGeneration.getModel(),
            imageGeneration.getSeed(),
            imageGeneration.getSteps(),
            imageGeneration.getCfgScale(),
            imageGeneration.getStatus().name(),
            imageGeneration.getIsPublic(),
            imageGeneration.getViewCount(),
            imageGeneration.getLikeCount(),
            imageGeneration.getCreatedAt(),
            imageGeneration.getUpdatedAt()
        );

        pgPool.preparedQuery(sql)
            .execute(params)
            .onSuccess(rows -> {
                if (rows.size() > 0) {
                    ImageGeneration saved = mapRowToImageGeneration(rows.iterator().next());
                    logger.debug("保存图片生成记录成功，ID: {}", saved.getId());
                    promise.complete(saved);
                } else {
                    promise.fail(new RuntimeException("保存图片生成记录失败"));
                }
            })
            .onFailure(throwable -> {
                logger.error("保存图片生成记录失败", throwable);
                promise.fail(throwable);
            });

        return promise.future();
    }

    @Override
    public Future<Optional<ImageGeneration>> findById(Long id) {
        Promise<Optional<ImageGeneration>> promise = Promise.promise();

        String sql = "SELECT * FROM image_generations WHERE id = $1";

        pgPool.preparedQuery(sql)
            .execute(Tuple.of(id))
            .onSuccess(rows -> {
                if (rows.size() > 0) {
                    ImageGeneration imageGeneration = mapRowToImageGeneration(rows.iterator().next());
                    promise.complete(Optional.of(imageGeneration));
                } else {
                    promise.complete(Optional.empty());
                }
            })
            .onFailure(throwable -> {
                logger.error("根据ID查找图片生成记录失败，ID: {}", id, throwable);
                promise.fail(throwable);
            });

        return promise.future();
    }

    @Override
    public Future<List<ImageGeneration>> findByUserId(Long userId, int page, int size) {
        Promise<List<ImageGeneration>> promise = Promise.promise();

        String sql = """
            SELECT * FROM image_generations 
            WHERE user_id = $1 
            ORDER BY created_at DESC 
            LIMIT $2 OFFSET $3
            """;

        int offset = page * size;
        Tuple params = Tuple.of(userId, size, offset);

        pgPool.preparedQuery(sql)
            .execute(params)
            .onSuccess(rows -> {
                List<ImageGeneration> generations = new ArrayList<>();
                for (Row row : rows) {
                    generations.add(mapRowToImageGeneration(row));
                }
                promise.complete(generations);
            })
            .onFailure(throwable -> {
                logger.error("根据用户ID查找图片生成记录失败，用户ID: {}", userId, throwable);
                promise.fail(throwable);
            });

        return promise.future();
    }

    @Override
    public Future<List<ImageGeneration>> findPublicGenerations(int page, int size) {
        Promise<List<ImageGeneration>> promise = Promise.promise();

        String sql = """
            SELECT * FROM image_generations 
            WHERE is_public = true AND status = 'COMPLETED'
            ORDER BY created_at DESC 
            LIMIT $1 OFFSET $2
            """;

        int offset = page * size;
        Tuple params = Tuple.of(size, offset);

        pgPool.preparedQuery(sql)
            .execute(params)
            .onSuccess(rows -> {
                List<ImageGeneration> generations = new ArrayList<>();
                for (Row row : rows) {
                    generations.add(mapRowToImageGeneration(row));
                }
                promise.complete(generations);
            })
            .onFailure(throwable -> {
                logger.error("查找公开图片生成记录失败", throwable);
                promise.fail(throwable);
            });

        return promise.future();
    }

    @Override
    public Future<List<ImageGeneration>> findByGenerationType(ImageGeneration.GenerationType type, int page, int size) {
        Promise<List<ImageGeneration>> promise = Promise.promise();

        String sql = """
            SELECT * FROM image_generations 
            WHERE generation_type = $1 AND is_public = true AND status = 'COMPLETED'
            ORDER BY created_at DESC 
            LIMIT $2 OFFSET $3
            """;

        int offset = page * size;
        Tuple params = Tuple.of(type.name(), size, offset);

        pgPool.preparedQuery(sql)
            .execute(params)
            .onSuccess(rows -> {
                List<ImageGeneration> generations = new ArrayList<>();
                for (Row row : rows) {
                    generations.add(mapRowToImageGeneration(row));
                }
                promise.complete(generations);
            })
            .onFailure(throwable -> {
                logger.error("根据生成类型查找图片生成记录失败，类型: {}", type, throwable);
                promise.fail(throwable);
            });

        return promise.future();
    }

    @Override
    public Future<List<ImageGeneration>> findByStatus(ImageGeneration.GenerationStatus status, int page, int size) {
        Promise<List<ImageGeneration>> promise = Promise.promise();

        String sql = """
            SELECT * FROM image_generations 
            WHERE status = $1 
            ORDER BY created_at DESC 
            LIMIT $2 OFFSET $3
            """;

        int offset = page * size;
        Tuple params = Tuple.of(status.name(), size, offset);

        pgPool.preparedQuery(sql)
            .execute(params)
            .onSuccess(rows -> {
                List<ImageGeneration> generations = new ArrayList<>();
                for (Row row : rows) {
                    generations.add(mapRowToImageGeneration(row));
                }
                promise.complete(generations);
            })
            .onFailure(throwable -> {
                logger.error("根据状态查找图片生成记录失败，状态: {}", status, throwable);
                promise.fail(throwable);
            });

        return promise.future();
    }

    @Override
    public Future<ImageGeneration> update(ImageGeneration imageGeneration) {
        Promise<ImageGeneration> promise = Promise.promise();

        String sql = """
            UPDATE image_generations SET 
                generation_type = $2, prompt = $3, negative_prompt = $4, original_image_url = $5,
                generated_image_url = $6, thumbnail_url = $7, width = $8, height = $9, style = $10,
                model = $11, seed = $12, steps = $13, cfg_scale = $14, status = $15, error_message = $16,
                generation_time = $17, is_public = $18, view_count = $19, like_count = $20, updated_at = $21
            WHERE id = $1
            RETURNING *
            """;

        Tuple params = Tuple.of(
            imageGeneration.getId(),
            imageGeneration.getGenerationType().name(),
            imageGeneration.getPrompt(),
            imageGeneration.getNegativePrompt(),
            imageGeneration.getOriginalImageUrl(),
            imageGeneration.getGeneratedImageUrl(),
            imageGeneration.getThumbnailUrl(),
            imageGeneration.getWidth(),
            imageGeneration.getHeight(),
            imageGeneration.getStyle(),
            imageGeneration.getModel(),
            imageGeneration.getSeed(),
            imageGeneration.getSteps(),
            imageGeneration.getCfgScale(),
            imageGeneration.getStatus().name(),
            imageGeneration.getErrorMessage(),
            imageGeneration.getGenerationTime(),
            imageGeneration.getIsPublic(),
            imageGeneration.getViewCount(),
            imageGeneration.getLikeCount(),
            LocalDateTime.now()
        );

        pgPool.preparedQuery(sql)
            .execute(params)
            .onSuccess(rows -> {
                if (rows.size() > 0) {
                    ImageGeneration updated = mapRowToImageGeneration(rows.iterator().next());
                    logger.debug("更新图片生成记录成功，ID: {}", updated.getId());
                    promise.complete(updated);
                } else {
                    promise.fail(new RuntimeException("更新图片生成记录失败，记录不存在"));
                }
            })
            .onFailure(throwable -> {
                logger.error("更新图片生成记录失败，ID: {}", imageGeneration.getId(), throwable);
                promise.fail(throwable);
            });

        return promise.future();
    }

    @Override
    public Future<Boolean> deleteById(Long id) {
        Promise<Boolean> promise = Promise.promise();

        String sql = "DELETE FROM image_generations WHERE id = $1";

        pgPool.preparedQuery(sql)
            .execute(Tuple.of(id))
            .onSuccess(rows -> {
                boolean deleted = rows.rowCount() > 0;
                logger.debug("删除图片生成记录，ID: {}, 结果: {}", id, deleted);
                promise.complete(deleted);
            })
            .onFailure(throwable -> {
                logger.error("删除图片生成记录失败，ID: {}", id, throwable);
                promise.fail(throwable);
            });

        return promise.future();
    }

    @Override
    public Future<Long> countByUserId(Long userId) {
        Promise<Long> promise = Promise.promise();

        String sql = "SELECT COUNT(*) FROM image_generations WHERE user_id = $1";

        pgPool.preparedQuery(sql)
            .execute(Tuple.of(userId))
            .onSuccess(rows -> {
                Long count = rows.iterator().next().getLong(0);
                promise.complete(count);
            })
            .onFailure(throwable -> {
                logger.error("统计用户图片生成记录失败，用户ID: {}", userId, throwable);
                promise.fail(throwable);
            });

        return promise.future();
    }

    @Override
    public Future<Long> countPublicGenerations() {
        Promise<Long> promise = Promise.promise();

        String sql = "SELECT COUNT(*) FROM image_generations WHERE is_public = true AND status = 'COMPLETED'";

        pgPool.preparedQuery(sql)
            .execute()
            .onSuccess(rows -> {
                Long count = rows.iterator().next().getLong(0);
                promise.complete(count);
            })
            .onFailure(throwable -> {
                logger.error("统计公开图片生成记录失败", throwable);
                promise.fail(throwable);
            });

        return promise.future();
    }

    @Override
    public Future<Void> incrementViewCount(Long id) {
        Promise<Void> promise = Promise.promise();

        String sql = "UPDATE image_generations SET view_count = view_count + 1, updated_at = $2 WHERE id = $1";

        pgPool.preparedQuery(sql)
            .execute(Tuple.of(id, LocalDateTime.now()))
            .onSuccess(rows -> promise.complete())
            .onFailure(throwable -> {
                logger.error("增加查看次数失败，ID: {}", id, throwable);
                promise.fail(throwable);
            });

        return promise.future();
    }

    @Override
    public Future<Void> incrementLikeCount(Long id) {
        Promise<Void> promise = Promise.promise();

        String sql = "UPDATE image_generations SET like_count = like_count + 1, updated_at = $2 WHERE id = $1";

        pgPool.preparedQuery(sql)
            .execute(Tuple.of(id, LocalDateTime.now()))
            .onSuccess(rows -> promise.complete())
            .onFailure(throwable -> {
                logger.error("增加点赞次数失败，ID: {}", id, throwable);
                promise.fail(throwable);
            });

        return promise.future();
    }

    @Override
    public Future<List<ImageGeneration>> findPopularGenerations(int limit) {
        Promise<List<ImageGeneration>> promise = Promise.promise();

        String sql = """
            SELECT * FROM image_generations 
            WHERE is_public = true AND status = 'COMPLETED'
            ORDER BY (view_count + like_count * 2) DESC, created_at DESC
            LIMIT $1
            """;

        pgPool.preparedQuery(sql)
            .execute(Tuple.of(limit))
            .onSuccess(rows -> {
                List<ImageGeneration> generations = new ArrayList<>();
                for (Row row : rows) {
                    generations.add(mapRowToImageGeneration(row));
                }
                promise.complete(generations);
            })
            .onFailure(throwable -> {
                logger.error("获取热门图片生成记录失败", throwable);
                promise.fail(throwable);
            });

        return promise.future();
    }

    @Override
    public Future<List<ImageGeneration>> findLatestGenerations(int limit) {
        Promise<List<ImageGeneration>> promise = Promise.promise();

        String sql = """
            SELECT * FROM image_generations 
            WHERE is_public = true AND status = 'COMPLETED'
            ORDER BY created_at DESC
            LIMIT $1
            """;

        pgPool.preparedQuery(sql)
            .execute(Tuple.of(limit))
            .onSuccess(rows -> {
                List<ImageGeneration> generations = new ArrayList<>();
                for (Row row : rows) {
                    generations.add(mapRowToImageGeneration(row));
                }
                promise.complete(generations);
            })
            .onFailure(throwable -> {
                logger.error("获取最新图片生成记录失败", throwable);
                promise.fail(throwable);
            });

        return promise.future();
    }

    @Override
    public Future<List<ImageGeneration>> searchByKeyword(String keyword, int page, int size) {
        Promise<List<ImageGeneration>> promise = Promise.promise();

        String sql = """
            SELECT * FROM image_generations 
            WHERE is_public = true AND status = 'COMPLETED'
            AND (prompt ILIKE $1 OR style ILIKE $1)
            ORDER BY created_at DESC
            LIMIT $2 OFFSET $3
            """;

        int offset = page * size;
        String searchPattern = "%" + keyword + "%";
        Tuple params = Tuple.of(searchPattern, size, offset);

        pgPool.preparedQuery(sql)
            .execute(params)
            .onSuccess(rows -> {
                List<ImageGeneration> generations = new ArrayList<>();
                for (Row row : rows) {
                    generations.add(mapRowToImageGeneration(row));
                }
                promise.complete(generations);
            })
            .onFailure(throwable -> {
                logger.error("根据关键词搜索图片生成记录失败，关键词: {}", keyword, throwable);
                promise.fail(throwable);
            });

        return promise.future();
    }

    /**
     * 将数据库行映射为ImageGeneration对象
     */
    private ImageGeneration mapRowToImageGeneration(Row row) {
        ImageGeneration generation = new ImageGeneration();
        generation.setId(row.getLong("id"));
        generation.setUserId(row.getLong("user_id"));
        generation.setGenerationType(ImageGeneration.GenerationType.valueOf(row.getString("generation_type")));
        generation.setPrompt(row.getString("prompt"));
        generation.setNegativePrompt(row.getString("negative_prompt"));
        generation.setOriginalImageUrl(row.getString("original_image_url"));
        generation.setGeneratedImageUrl(row.getString("generated_image_url"));
        generation.setThumbnailUrl(row.getString("thumbnail_url"));
        generation.setWidth(row.getInteger("width"));
        generation.setHeight(row.getInteger("height"));
        generation.setStyle(row.getString("style"));
        generation.setModel(row.getString("model"));
        generation.setSeed(row.getLong("seed"));
        generation.setSteps(row.getInteger("steps"));
        generation.setCfgScale(row.getDouble("cfg_scale"));
        generation.setStatus(ImageGeneration.GenerationStatus.valueOf(row.getString("status")));
        generation.setErrorMessage(row.getString("error_message"));
        generation.setGenerationTime(row.getInteger("generation_time"));
        generation.setIsPublic(row.getBoolean("is_public"));
        generation.setViewCount(row.getInteger("view_count"));
        generation.setLikeCount(row.getInteger("like_count"));
        generation.setCreatedAt(row.getLocalDateTime("created_at"));
        generation.setUpdatedAt(row.getLocalDateTime("updated_at"));
        return generation;
    }
}
