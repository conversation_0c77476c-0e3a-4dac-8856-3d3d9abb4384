package com.superblog;

/**
 * SuperBlog 启动选择器
 * 提供多种启动方式的选择
 */
public class StartupSelector {

    public static void main(String[] args) {
        System.out.println("=".repeat(60));
        System.out.println("🎯 SuperBlog 个人技术展示平台");
        System.out.println("=".repeat(60));
        System.out.println();
        System.out.println("请选择启动方式：");
        System.out.println();
        System.out.println("1. MinimalApplication    - 最简版本（推荐，确保能启动）");
        System.out.println("2. Application          - 完整版本（带配置文件和日志）");
        System.out.println("3. SimpleApplication    - 简化版本（中等复杂度）");
        System.out.println("4. SimpleMainVerticle   - Verticle版本");
        System.out.println();
        System.out.println("=".repeat(60));
        System.out.println("🚀 启动说明：");
        System.out.println();
        System.out.println("【推荐】在IDE中运行：");
        System.out.println("1. 右键点击 MinimalApplication.java");
        System.out.println("2. 选择 'Run MinimalApplication.main()'");
        System.out.println();
        System.out.println("🧪 测试API：");
        System.out.println("启动成功后运行: ./minimal-test.sh");
        System.out.println();
        System.out.println("📋 可用接口：");
        System.out.println("- GET  /api/health                    健康检查");
        System.out.println("- POST /api/ai/text-to-image          AI文生图");
        System.out.println("- POST /api/ai/chat                   通义千问问答");
        System.out.println("- POST /api/ai/generate-article       生成博客文章");
        System.out.println("- POST /api/ai/optimize-prompt        优化绘画提示词");
        System.out.println("- GET  /api/ai/generations/public     获取图片记录");
        System.out.println("- GET  /api/ai/qwen/test              API连接测试");
        System.out.println();
        System.out.println("🔑 通义千问API密钥: sk-4606dfde828a4f9aa7a43f5d53dddb9e");
        System.out.println();
        System.out.println("=".repeat(60));
        System.out.println("💡 提示：如果遇到启动问题，请使用 MinimalApplication");
        System.out.println("=".repeat(60));

        // 默认启动MinimalApplication
        System.out.println();
        System.out.println("🚀 自动启动 MinimalApplication...");
        System.out.println();
        
        try {
            MinimalApplication.main(args);
        } catch (Exception e) {
            System.err.println("❌ 启动失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
