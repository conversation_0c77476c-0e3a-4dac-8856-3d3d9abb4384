/**
 * AI图片生成页面
 * 提供文生图和图生图功能
 */

import React, { useState, useRef } from 'react';
import {
  Image as ImageIcon,
  Upload,
  Download,
  Heart,
  Eye,
  Copy,
  Shuffle,
  <PERSON>tings,
  Sparkles,
  Loader2,
  Refresh<PERSON><PERSON>,
  Share2,
  Trash2
} from 'lucide-react';
import { Button, IconButton } from '@/components/ui/Button';
import { Input, Textarea } from '@/components/ui/Input';
import { useImageStore } from '@/stores/aiStore';
import { copyToClipboard, formatRelativeTime, formatNumber } from '@/utils';
import toast from 'react-hot-toast';

const AIImagePage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'generate' | 'gallery'>('generate');
  const [generationType, setGenerationType] = useState<'text-to-image' | 'image-to-image'>('text-to-image');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const {
    generations,
    currentGeneration,
    loading,
    error,
    formData,
    updateFormData,
    resetFormData,
    createTextToImage,
    loadPublicGenerations,
    likeGeneration,
  } = useImageStore();

  // 生成图片
  const handleGenerate = async () => {
    if (!formData.prompt?.trim()) {
      toast.error('请输入提示词');
      return;
    }

    try {
      await createTextToImage({
        ...formData,
        prompt: formData.prompt!,
      });
      toast.success('图片生成成功！');
    } catch (error) {
      toast.error('生成失败，请重试');
    }
  };

  // 上传图片
  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        updateFormData({ original_image_url: e.target?.result as string });
      };
      reader.readAsDataURL(file);
    }
  };

  // 随机提示词
  const handleRandomPrompt = () => {
    const randomPrompts = [
      '一只可爱的橘猫坐在樱花树下，春天的阳光透过花瓣洒下',
      '未来科技城市的夜景，霓虹灯闪烁，飞行汽车穿梭其间',
      '古风美女在竹林中弹琴，月光如水，意境优美',
      '宇宙中的星云，色彩绚烂，充满神秘感',
      '森林中的小木屋，炊烟袅袅，温馨宁静',
    ];
    const randomPrompt = randomPrompts[Math.floor(Math.random() * randomPrompts.length)];
    updateFormData({ prompt: randomPrompt });
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
          AI图片生成
        </h1>
        <p className="text-gray-600 dark:text-gray-300">
          使用先进的AI技术，将文字描述转换为精美图片
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="flex space-x-1 mb-6 bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
        <TabButton
          active={activeTab === 'generate'}
          onClick={() => setActiveTab('generate')}
          icon={<Sparkles className="w-4 h-4" />}
          label="图片生成"
        />
        <TabButton
          active={activeTab === 'gallery'}
          onClick={() => setActiveTab('gallery')}
          icon={<ImageIcon className="w-4 h-4" />}
          label="作品画廊"
        />
      </div>

      {/* Error Display */}
      {error && (
        <div className="mb-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <p className="text-red-700 dark:text-red-300">{error}</p>
        </div>
      )}

      {/* Tab Content */}
      {activeTab === 'generate' ? (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Generation Panel */}
          <div className="lg:col-span-2 space-y-6">
            <GenerationPanel
              generationType={generationType}
              setGenerationType={setGenerationType}
              formData={formData}
              updateFormData={updateFormData}
              onGenerate={handleGenerate}
              onRandomPrompt={handleRandomPrompt}
              onImageUpload={handleImageUpload}
              fileInputRef={fileInputRef}
              loading={loading}
            />
          </div>

          {/* Result Panel */}
          <div>
            <ResultPanel
              currentGeneration={currentGeneration}
              loading={loading}
              onLike={likeGeneration}
            />
          </div>
        </div>
      ) : (
        <GalleryPanel
          generations={generations}
          onLoadMore={loadPublicGenerations}
          onLike={likeGeneration}
        />
      )}
    </div>
  );
};

// Tab按钮组件
interface TabButtonProps {
  active: boolean;
  onClick: () => void;
  icon: React.ReactNode;
  label: string;
}

const TabButton: React.FC<TabButtonProps> = ({ active, onClick, icon, label }) => (
  <button
    onClick={onClick}
    className={`
      flex items-center space-x-2 px-4 py-2 rounded-md font-medium transition-colors
      ${active
        ? 'bg-white dark:bg-gray-700 text-blue-600 dark:text-blue-400 shadow-sm'
        : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200'
      }
    `}
  >
    {icon}
    <span>{label}</span>
  </button>
);

// 生成面板组件
interface GenerationPanelProps {
  generationType: 'text-to-image' | 'image-to-image';
  setGenerationType: (type: 'text-to-image' | 'image-to-image') => void;
  formData: any;
  updateFormData: (data: any) => void;
  onGenerate: () => void;
  onRandomPrompt: () => void;
  onImageUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
  fileInputRef: React.RefObject<HTMLInputElement>;
  loading: boolean;
}

const GenerationPanel: React.FC<GenerationPanelProps> = ({
  generationType,
  setGenerationType,
  formData,
  updateFormData,
  onGenerate,
  onRandomPrompt,
  onImageUpload,
  fileInputRef,
  loading,
}) => (
  <div className="space-y-6">
    {/* Generation Type */}
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">生成类型</h3>
      <div className="grid grid-cols-2 gap-4">
        <button
          onClick={() => setGenerationType('text-to-image')}
          className={`p-4 rounded-lg border-2 transition-colors ${
            generationType === 'text-to-image'
              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
              : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
          }`}
        >
          <Sparkles className="w-8 h-8 mx-auto mb-2 text-blue-500" />
          <h4 className="font-medium text-gray-900 dark:text-white">文生图</h4>
          <p className="text-sm text-gray-600 dark:text-gray-300">根据文字描述生成图片</p>
        </button>

        <button
          onClick={() => setGenerationType('image-to-image')}
          className={`p-4 rounded-lg border-2 transition-colors ${
            generationType === 'image-to-image'
              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
              : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
          }`}
        >
          <ImageIcon className="w-8 h-8 mx-auto mb-2 text-purple-500" />
          <h4 className="font-medium text-gray-900 dark:text-white">图生图</h4>
          <p className="text-sm text-gray-600 dark:text-gray-300">基于原图进行风格转换</p>
        </button>
      </div>
    </div>

    {/* Prompt Input */}
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">提示词</h3>
        <Button
          variant="outline"
          size="sm"
          onClick={onRandomPrompt}
          icon={<Shuffle className="w-4 h-4" />}
        >
          随机
        </Button>
      </div>

      <div className="space-y-4">
        <Textarea
          value={formData.prompt || ''}
          onChange={(e) => updateFormData({ prompt: e.target.value })}
          placeholder="描述你想要生成的图片，例如：一只可爱的橘猫坐在樱花树下..."
          rows={4}
          fullWidth
        />

        <Textarea
          label="负面提示词（可选）"
          value={formData.negative_prompt || ''}
          onChange={(e) => updateFormData({ negative_prompt: e.target.value })}
          placeholder="描述不希望出现的内容，例如：模糊、低质量、变形..."
          rows={2}
          fullWidth
        />
      </div>
    </div>

    {/* Image Upload (for image-to-image) */}
    {generationType === 'image-to-image' && (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">原始图片</h3>
        <div className="space-y-4">
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={onImageUpload}
            className="hidden"
          />

          {formData.original_image_url ? (
            <div className="relative">
              <img
                src={formData.original_image_url}
                alt="原始图片"
                className="w-full h-48 object-cover rounded-lg"
              />
              <button
                onClick={() => updateFormData({ original_image_url: null })}
                className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600"
              >
                <Trash2 className="w-4 h-4" />
              </button>
            </div>
          ) : (
            <button
              onClick={() => fileInputRef.current?.click()}
              className="w-full h-48 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg flex flex-col items-center justify-center hover:border-gray-400 dark:hover:border-gray-500 transition-colors"
            >
              <Upload className="w-8 h-8 text-gray-400 mb-2" />
              <span className="text-gray-600 dark:text-gray-300">点击上传图片</span>
            </button>
          )}
        </div>
      </div>
    )}

    {/* Generation Parameters */}
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
      <div className="flex items-center space-x-2 mb-4">
        <Settings className="w-5 h-5 text-gray-600 dark:text-gray-300" />
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">生成参数</h3>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            图片尺寸
          </label>
          <select
            value={`${formData.width}x${formData.height}`}
            onChange={(e) => {
              const [width, height] = e.target.value.split('x').map(Number);
              updateFormData({ width, height });
            }}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="512x512">512×512 (正方形)</option>
            <option value="768x512">768×512 (横向)</option>
            <option value="512x768">512×768 (纵向)</option>
            <option value="1024x1024">1024×1024 (高清正方形)</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            艺术风格
          </label>
          <select
            value={formData.style || '写实风格'}
            onChange={(e) => updateFormData({ style: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="写实风格">写实风格</option>
            <option value="动漫风格">动漫风格</option>
            <option value="油画风格">油画风格</option>
            <option value="水彩风格">水彩风格</option>
            <option value="素描风格">素描风格</option>
            <option value="科幻风格">科幻风格</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            AI模型
          </label>
          <select
            value={formData.model || 'dall-e-3'}
            onChange={(e) => updateFormData({ model: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="dall-e-3">DALL-E 3 (OpenAI)</option>
            <option value="dall-e-2">DALL-E 2 (OpenAI)</option>
            <option value="qwen-vl">通义千问VL (阿里云)</option>
            <option value="stable-diffusion">Stable Diffusion</option>
            <option value="midjourney">Midjourney</option>
            <option value="stable-diffusion-xl">Stable Diffusion XL</option>
            <option value="wanx">万相大模型 (阿里云)</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            生成步数: {formData.steps || 20}
          </label>
          <input
            type="range"
            min="10"
            max="50"
            value={formData.steps || 20}
            onChange={(e) => updateFormData({ steps: parseInt(e.target.value) })}
            className="w-full"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            引导强度: {formData.cfg_scale || 7}
          </label>
          <input
            type="range"
            min="1"
            max="20"
            step="0.5"
            value={formData.cfg_scale || 7}
            onChange={(e) => updateFormData({ cfg_scale: parseFloat(e.target.value) })}
            className="w-full"
          />
        </div>
      </div>

      <div className="mt-4 flex items-center space-x-2">
        <input
          type="checkbox"
          id="is_public"
          checked={formData.is_public !== false}
          onChange={(e) => updateFormData({ is_public: e.target.checked })}
          className="rounded"
        />
        <label htmlFor="is_public" className="text-sm text-gray-700 dark:text-gray-300">
          公开分享（其他用户可以在画廊中看到）
        </label>
      </div>
    </div>

    {/* Generate Button */}
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
      <Button
        onClick={onGenerate}
        disabled={!formData.prompt?.trim() || loading}
        loading={loading}
        size="lg"
        fullWidth
        icon={<Sparkles className="w-5 h-5" />}
      >
        {loading ? '生成中...' : '开始生成'}
      </Button>
    </div>
  </div>
);

// 结果面板组件
interface ResultPanelProps {
  currentGeneration: any;
  loading: boolean;
  onLike: (id: number) => void;
}

const ResultPanel: React.FC<ResultPanelProps> = ({
  currentGeneration,
  loading,
  onLike,
}) => (
  <div className="space-y-6">
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">生成结果</h3>

      {loading ? (
        <div className="aspect-square bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
          <div className="text-center">
            <Loader2 className="w-8 h-8 text-blue-500 animate-spin mx-auto mb-2" />
            <p className="text-gray-600 dark:text-gray-300">AI正在创作中...</p>
          </div>
        </div>
      ) : currentGeneration ? (
        <div className="space-y-4">
          <div className="relative group">
            <img
              src={currentGeneration.generated_image_url || '/placeholder-image.svg'}
              alt="生成的图片"
              className="w-full aspect-square object-cover rounded-lg"
              onError={(e) => {
                e.currentTarget.src = '/placeholder-image.svg';
              }}
            />
            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100">
              <div className="flex space-x-2">
                <IconButton
                  variant="secondary"
                  size="sm"
                  onClick={() => {
                    const link = document.createElement('a');
                    link.href = currentGeneration.generated_image_url;
                    link.download = `ai-image-${currentGeneration.id}.jpg`;
                    link.click();
                  }}
                  icon={<Download className="w-4 h-4" />}
                  aria-label="下载图片"
                />
                <IconButton
                  variant="secondary"
                  size="sm"
                  onClick={() => copyToClipboard(currentGeneration.generated_image_url)}
                  icon={<Copy className="w-4 h-4" />}
                  aria-label="复制链接"
                />
                <IconButton
                  variant="secondary"
                  size="sm"
                  onClick={() => navigator.share?.({
                    title: '我用AI生成的图片',
                    url: currentGeneration.generated_image_url,
                  })}
                  icon={<Share2 className="w-4 h-4" />}
                  aria-label="分享"
                />
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-1">提示词</h4>
              <p className="text-sm text-gray-600 dark:text-gray-300">{currentGeneration.prompt}</p>
            </div>

            <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
              <span>{currentGeneration.width}×{currentGeneration.height}</span>
              <span>{currentGeneration.style}</span>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                <span className="flex items-center space-x-1">
                  <Eye className="w-4 h-4" />
                  <span>{formatNumber(currentGeneration.view_count || 0)}</span>
                </span>
                <span className="flex items-center space-x-1">
                  <Heart className="w-4 h-4" />
                  <span>{formatNumber(currentGeneration.like_count || 0)}</span>
                </span>
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() => onLike(currentGeneration.id)}
                icon={<Heart className="w-4 h-4" />}
              >
                点赞
              </Button>
            </div>
          </div>
        </div>
      ) : (
        <div className="aspect-square bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
          <div className="text-center">
            <ImageIcon className="w-12 h-12 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-600 dark:text-gray-300">生成的图片将在这里显示</p>
          </div>
        </div>
      )}
    </div>
  </div>
);

// 画廊面板组件
interface GalleryPanelProps {
  generations: any[];
  onLoadMore: () => void;
  onLike: (id: number) => void;
}

const GalleryPanel: React.FC<GalleryPanelProps> = ({
  generations,
  onLoadMore,
  onLike,
}) => {
  React.useEffect(() => {
    if (generations.length === 0) {
      onLoadMore();
    }
  }, []);

  return (
    <div className="space-y-6">
      {/* Gallery Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">作品画廊</h2>
        <Button
          variant="outline"
          onClick={onLoadMore}
          icon={<RefreshCw className="w-4 h-4" />}
        >
          刷新
        </Button>
      </div>

      {/* Gallery Grid */}
      {generations.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {generations.map((generation) => (
            <GalleryCard
              key={generation.id}
              generation={generation}
              onLike={onLike}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <ImageIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">暂无作品</h3>
          <p className="text-gray-600 dark:text-gray-300">开始生成你的第一张AI图片吧！</p>
        </div>
      )}

      {/* Load More */}
      {generations.length > 0 && (
        <div className="text-center">
          <Button
            variant="outline"
            onClick={onLoadMore}
          >
            加载更多
          </Button>
        </div>
      )}
    </div>
  );
};

// 画廊卡片组件
interface GalleryCardProps {
  generation: any;
  onLike: (id: number) => void;
}

const GalleryCard: React.FC<GalleryCardProps> = ({ generation, onLike }) => (
  <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden group hover:shadow-xl transition-shadow">
    <div className="relative">
      <img
        src={generation.generated_image_url || '/placeholder-image.svg'}
        alt={generation.prompt}
        className="w-full aspect-square object-cover"
        onError={(e) => {
          e.currentTarget.src = '/placeholder-image.svg';
        }}
      />
      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all flex items-center justify-center opacity-0 group-hover:opacity-100">
        <div className="flex space-x-2">
          <IconButton
            variant="secondary"
            size="sm"
            onClick={() => {
              const link = document.createElement('a');
              link.href = generation.generated_image_url;
              link.download = `ai-image-${generation.id}.jpg`;
              link.click();
            }}
            icon={<Download className="w-4 h-4" />}
            aria-label="下载图片"
          />
          <IconButton
            variant="secondary"
            size="sm"
            onClick={() => copyToClipboard(generation.generated_image_url)}
            icon={<Copy className="w-4 h-4" />}
            aria-label="复制链接"
          />
        </div>
      </div>
    </div>

    <div className="p-4">
      <p className="text-sm text-gray-600 dark:text-gray-300 mb-3 line-clamp-2">
        {generation.prompt}
      </p>

      <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-3">
        <span>{generation.width}×{generation.height}</span>
        <span>{generation.style}</span>
      </div>

      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3 text-sm text-gray-500 dark:text-gray-400">
          <span className="flex items-center space-x-1">
            <Eye className="w-4 h-4" />
            <span>{formatNumber(generation.view_count || 0)}</span>
          </span>
          <span className="flex items-center space-x-1">
            <Heart className="w-4 h-4" />
            <span>{formatNumber(generation.like_count || 0)}</span>
          </span>
        </div>

        <IconButton
          variant="ghost"
          size="sm"
          onClick={() => onLike(generation.id)}
          icon={<Heart className="w-4 h-4" />}
          aria-label="点赞"
        />
      </div>

      <div className="mt-2 text-xs text-gray-400">
        {formatRelativeTime(new Date(generation.created_at))}
      </div>
    </div>
  </div>
);

export default AIImagePage;
