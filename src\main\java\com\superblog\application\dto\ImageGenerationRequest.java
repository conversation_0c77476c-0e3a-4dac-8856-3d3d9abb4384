package com.superblog.application.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * AI图片生成请求DTO
 */
public class ImageGenerationRequest {

    @JsonProperty("user_id")
    private Long userId;

    @JsonProperty("prompt")
    private String prompt;

    @JsonProperty("negative_prompt")
    private String negativePrompt;

    @JsonProperty("original_image_url")
    private String originalImageUrl;

    @JsonProperty("width")
    private Integer width = 512;

    @JsonProperty("height")
    private Integer height = 512;

    @JsonProperty("style")
    private String style;

    @JsonProperty("model")
    private String model = "dall-e-3";

    @JsonProperty("seed")
    private Long seed;

    @JsonProperty("steps")
    private Integer steps = 20;

    @JsonProperty("cfg_scale")
    private Double cfgScale = 7.0;

    @JsonProperty("is_public")
    private Boolean isPublic = true;

    // 构造函数
    public ImageGenerationRequest() {}

    // Getter和Setter方法
    public Long getUserId() { return userId; }
    public void setUserId(Long userId) { this.userId = userId; }

    public String getPrompt() { return prompt; }
    public void setPrompt(String prompt) { this.prompt = prompt; }

    public String getNegativePrompt() { return negativePrompt; }
    public void setNegativePrompt(String negativePrompt) { this.negativePrompt = negativePrompt; }

    public String getOriginalImageUrl() { return originalImageUrl; }
    public void setOriginalImageUrl(String originalImageUrl) { this.originalImageUrl = originalImageUrl; }

    public Integer getWidth() { return width; }
    public void setWidth(Integer width) { this.width = width; }

    public Integer getHeight() { return height; }
    public void setHeight(Integer height) { this.height = height; }

    public String getStyle() { return style; }
    public void setStyle(String style) { this.style = style; }

    public String getModel() { return model; }
    public void setModel(String model) { this.model = model; }

    public Long getSeed() { return seed; }
    public void setSeed(Long seed) { this.seed = seed; }

    public Integer getSteps() { return steps; }
    public void setSteps(Integer steps) { this.steps = steps; }

    public Double getCfgScale() { return cfgScale; }
    public void setCfgScale(Double cfgScale) { this.cfgScale = cfgScale; }

    public Boolean getIsPublic() { return isPublic; }
    public void setIsPublic(Boolean isPublic) { this.isPublic = isPublic; }

    /**
     * 验证请求参数
     */
    public boolean isValid() {
        return prompt != null && !prompt.trim().isEmpty() &&
               userId != null &&
               width != null && width > 0 && width <= 2048 &&
               height != null && height > 0 && height <= 2048;
    }

    /**
     * 验证文生图请求
     */
    public boolean isValidForTextToImage() {
        return isValid();
    }

    /**
     * 验证图生图请求
     */
    public boolean isValidForImageToImage() {
        return isValid() && originalImageUrl != null && !originalImageUrl.trim().isEmpty();
    }
}
