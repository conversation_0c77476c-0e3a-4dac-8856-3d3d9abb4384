# SuperBlog 配置说明

## 配置文件结构

SuperBlog 使用基于环境的配置文件系统，支持以下环境：

- `local` - 本地开发环境
- `dev` - 开发环境
- `test` - 测试环境
- `prod` - 生产环境

## 配置文件

### 主配置文件
- `application.yml` - 主配置文件，定义默认环境为dev

### 环境特定配置文件
- `application-local.yml` - 本地开发环境配置
- `application-dev.yml` - 开发环境配置
- `application-test.yml` - 测试环境配置
- `application-prod.yml` - 生产环境配置

## 环境变量

生产环境推荐使用环境变量来配置敏感信息，如API密钥、数据库密码等。

参考 `.env.example` 文件中的环境变量配置。

## 配置优先级

配置的优先级从高到低：
1. 环境变量
2. 环境特定配置文件 (如 application-prod.yml)
3. 主配置文件 (application.yml)

## 启动时指定环境

### 通过JVM参数
```bash
java -Dspring.profiles.active=prod -jar superblog.jar
```

### 通过环境变量
```bash
export SPRING_PROFILES_ACTIVE=prod
java -jar superblog.jar
```

## 配置项说明

### 服务器配置
```yaml
server:
  port: 8080              # 服务端口
  servlet:
    context-path: /api    # API路径前缀
```

### 数据库配置
```yaml
spring:
  datasource:
    username: superblog
    password: superblog123
    url: ******************************************
    driver-class-name: org.postgresql.Driver
```

### Redis配置
```yaml
spring:
  redis:
    host: localhost
    port: 6379
    password: 
```

### AI服务配置
```yaml
ai:
  openai:
    base-url: https://api.openai.com/v1
    api-key: ${OPENAI_API_KEY}
    model: gpt-3.5-turbo
    timeout: 60000
    
  qwen:
    base-url: https://dashscope.aliyuncs.com/api/v1
    api-key: ${QWEN_API_KEY}
    model: qwen-turbo
    timeout: 60000
    
  stability:
    base-url: https://api.stability.ai/v1
    api-key: ${STABILITY_API_KEY}
    timeout: 120000
```

### 文件上传配置
```yaml
file:
  upload:
    path: ./uploads
    max-size: 10MB
    allowed-types: jpg,jpeg,png,gif,webp
```

### JWT配置
```yaml
jwt:
  secret: ${JWT_SECRET}
  expiration: 86400000    # 24小时，单位毫秒
```

### CORS配置
```yaml
cors:
  allowed-origins: http://localhost:3000
  allowed-methods: GET,POST,PUT,DELETE,OPTIONS
  allowed-headers: "*"
  max-age: 3600
```

## 开发环境快速开始

1. 复制环境变量配置文件：
```bash
cp .env.example .env
```

2. 编辑 `.env` 文件，设置必要的配置（如API密钥）

3. 启动应用：
```bash
mvn clean compile exec:java
```

应用将默认使用 `dev` 环境配置。

## 生产部署

1. 设置环境变量或创建 `application-prod.yml` 配置文件
2. 启动应用时指定生产环境：
```bash
java -Dspring.profiles.active=prod -jar superblog.jar
```

## 注意事项

- 生产环境中不要在配置文件中直接写入敏感信息（如API密钥、密码）
- 使用环境变量 `${VARIABLE_NAME}` 的方式引用敏感配置
- 测试环境使用内存数据库，无需配置外部数据库
- 本地开发环境的AI服务配置为空时，会使用mock数据
