package com.superblog.config;

import io.vertx.core.json.JsonObject;
import io.vertx.core.json.JsonArray;
import java.io.InputStream;
import java.util.Map;
import org.yaml.snakeyaml.Yaml;

/**
 * YAML配置读取器
 * 负责读取YAML配置文件并转换为JsonObject
 */
public class YamlConfigReader {
    
    /**
     * 从classpath读取YAML配置文件
     */
    public static JsonObject readYamlConfig(String fileName) {
        try (InputStream inputStream = YamlConfigReader.class.getClassLoader().getResourceAsStream(fileName)) {
            if (inputStream == null) {
                throw new RuntimeException("配置文件不存在: " + fileName);
            }
            
            Yaml yaml = new Yaml();
            Map<String, Object> yamlData = yaml.load(inputStream);
            
            return new JsonObject(yamlData);
        } catch (Exception e) {
            throw new RuntimeException("读取配置文件失败: " + fileName, e);
        }
    }
    
    /**
     * 读取应用配置
     * 会根据spring.profiles.active读取对应的配置文件
     */
    public static JsonObject loadApplicationConfig() {
        // 先读取主配置文件
        JsonObject mainConfig = readYamlConfig("application.yml");
        
        // 获取激活的profile
        String activeProfile = mainConfig.getJsonObject("spring", new JsonObject())
            .getJsonObject("profiles", new JsonObject())
            .getString("active", "dev");
        
        // 读取profile特定的配置文件
        String profileConfigFile = "application-" + activeProfile + ".yml";
        JsonObject profileConfig = readYamlConfig(profileConfigFile);
        
        // 合并配置（profile配置覆盖主配置）
        return mergeConfig(mainConfig, profileConfig);
    }
    
    /**
     * 合并两个配置对象
     */
    private static JsonObject mergeConfig(JsonObject base, JsonObject override) {
        JsonObject merged = base.copy();
        
        for (Map.Entry<String, Object> entry : override) {
            String key = entry.getKey();
            Object value = entry.getValue();
            
            if (value instanceof Map && merged.containsKey(key)) {
                // 递归合并嵌套对象
                JsonObject baseNested = merged.getJsonObject(key);
                JsonObject overrideNested = new JsonObject((Map<String, Object>) value);
                merged.put(key, mergeConfig(baseNested, overrideNested));
            } else {
                merged.put(key, value);
            }
        }
        
        return merged;
    }
}
