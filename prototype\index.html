<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SuperBlog - 个人技术展示平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .card-hover { transition: transform 0.3s ease, box-shadow 0.3s ease; }
        .card-hover:hover { transform: translateY(-5px); box-shadow: 0 10px 25px rgba(0,0,0,0.1); }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-2xl font-bold text-gray-800">SuperBlog</h1>
                    </div>
                </div>
                <div class="flex items-center space-x-8">
                    <button onclick="showTab('home')" class="tab-btn text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium active">
                        <i class="fas fa-home mr-2"></i>首页
                    </button>
                    <button onclick="showTab('ai-tools')" class="tab-btn text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-magic mr-2"></i>AI图片生成
                    </button>
                    <button onclick="showTab('videos')" class="tab-btn text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-video mr-2"></i>视频展示
                    </button>
                    <button onclick="showTab('blog')" class="tab-btn text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-blog mr-2"></i>博客
                    </button>
                    <button onclick="showTab('projects')" class="tab-btn text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-code mr-2"></i>项目
                    </button>
                    <button onclick="showTab('tools')" class="tab-btn text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-tools mr-2"></i>小工具
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        <!-- 首页 -->
        <div id="home" class="tab-content active">
            <!-- 个人简介卡片 -->
            <div class="gradient-bg rounded-lg p-8 text-white mb-8">
                <div class="flex items-center space-x-6">
                    <img src="https://via.placeholder.com/120x120/4F46E5/FFFFFF?text=Avatar" alt="头像" class="w-24 h-24 rounded-full border-4 border-white">
                    <div>
                        <h2 class="text-3xl font-bold mb-2">张三</h2>
                        <p class="text-xl mb-3">全栈开发工程师 & AI爱好者</p>
                        <div class="flex space-x-2">
                            <span class="bg-white bg-opacity-20 px-3 py-1 rounded-full text-sm">Java</span>
                            <span class="bg-white bg-opacity-20 px-3 py-1 rounded-full text-sm">React</span>
                            <span class="bg-white bg-opacity-20 px-3 py-1 rounded-full text-sm">AI/ML</span>
                            <span class="bg-white bg-opacity-20 px-3 py-1 rounded-full text-sm">Vert.x</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 统计面板 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-lg p-6 shadow-md card-hover">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                            <i class="fas fa-image text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">生成图片</p>
                            <p class="text-2xl font-semibold text-gray-900">1,234</p>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-lg p-6 shadow-md card-hover">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-green-100 text-green-600">
                            <i class="fas fa-video text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">视频数量</p>
                            <p class="text-2xl font-semibold text-gray-900">56</p>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-lg p-6 shadow-md card-hover">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                            <i class="fas fa-blog text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">博客文章</p>
                            <p class="text-2xl font-semibold text-gray-900">89</p>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-lg p-6 shadow-md card-hover">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-orange-100 text-orange-600">
                            <i class="fas fa-eye text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">总访问量</p>
                            <p class="text-2xl font-semibold text-gray-900">12.5K</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 最新动态 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div class="bg-white rounded-lg p-6 shadow-md">
                    <h3 class="text-lg font-semibold mb-4 flex items-center">
                        <i class="fas fa-magic text-blue-600 mr-2"></i>最新生成的图片
                    </h3>
                    <div class="grid grid-cols-2 gap-4">
                        <img src="https://via.placeholder.com/200x200/FF6B6B/FFFFFF?text=AI+Image+1" alt="AI生成图片" class="rounded-lg">
                        <img src="https://via.placeholder.com/200x200/4ECDC4/FFFFFF?text=AI+Image+2" alt="AI生成图片" class="rounded-lg">
                    </div>
                </div>
                <div class="bg-white rounded-lg p-6 shadow-md">
                    <h3 class="text-lg font-semibold mb-4 flex items-center">
                        <i class="fas fa-blog text-purple-600 mr-2"></i>最新博客文章
                    </h3>
                    <div class="space-y-3">
                        <div class="border-l-4 border-blue-500 pl-4">
                            <h4 class="font-medium">使用Vert.x构建高性能Web应用</h4>
                            <p class="text-sm text-gray-600">2024-01-15</p>
                        </div>
                        <div class="border-l-4 border-green-500 pl-4">
                            <h4 class="font-medium">AI图像生成技术深度解析</h4>
                            <p class="text-sm text-gray-600">2024-01-12</p>
                        </div>
                        <div class="border-l-4 border-purple-500 pl-4">
                            <h4 class="font-medium">React 18新特性实战指南</h4>
                            <p class="text-sm text-gray-600">2024-01-10</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- AI图片生成工具 -->
        <div id="ai-tools" class="tab-content">
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-2xl font-bold mb-6 flex items-center">
                    <i class="fas fa-magic text-blue-600 mr-3"></i>AI图片生成工具
                </h2>
                
                <!-- 功能选择 -->
                <div class="flex space-x-4 mb-6">
                    <button onclick="showAITool('text-to-image')" class="ai-tool-btn bg-blue-600 text-white px-6 py-2 rounded-lg active">
                        <i class="fas fa-font mr-2"></i>文生图
                    </button>
                    <button onclick="showAITool('image-to-image')" class="ai-tool-btn bg-gray-200 text-gray-700 px-6 py-2 rounded-lg">
                        <i class="fas fa-image mr-2"></i>图生图
                    </button>
                </div>

                <!-- 文生图界面 -->
                <div id="text-to-image" class="ai-tool-content">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <div>
                            <h3 class="text-lg font-semibold mb-4">输入描述</h3>
                            <textarea class="w-full h-32 p-4 border border-gray-300 rounded-lg resize-none" 
                                placeholder="请描述你想要生成的图片，例如：一只可爱的橘猫坐在樱花树下，动漫风格，高清画质"></textarea>
                            
                            <div class="mt-4 grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">图片尺寸</label>
                                    <select class="w-full p-2 border border-gray-300 rounded-lg">
                                        <option>512x512</option>
                                        <option>768x768</option>
                                        <option>1024x1024</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">艺术风格</label>
                                    <select class="w-full p-2 border border-gray-300 rounded-lg">
                                        <option>写实风格</option>
                                        <option>动漫风格</option>
                                        <option>油画风格</option>
                                        <option>水彩风格</option>
                                    </select>
                                </div>
                            </div>
                            
                            <button class="w-full mt-6 bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 transition-colors">
                                <i class="fas fa-magic mr-2"></i>生成图片
                            </button>
                        </div>
                        
                        <div>
                            <h3 class="text-lg font-semibold mb-4">生成结果</h3>
                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                                <i class="fas fa-image text-4xl text-gray-400 mb-4"></i>
                                <p class="text-gray-500">生成的图片将显示在这里</p>
                            </div>
                            <div class="mt-4 flex space-x-2">
                                <button class="flex-1 bg-green-600 text-white py-2 rounded-lg hover:bg-green-700">
                                    <i class="fas fa-download mr-2"></i>下载
                                </button>
                                <button class="flex-1 bg-purple-600 text-white py-2 rounded-lg hover:bg-purple-700">
                                    <i class="fas fa-share mr-2"></i>分享
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 图生图界面 -->
                <div id="image-to-image" class="ai-tool-content" style="display: none;">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <div>
                            <h3 class="text-lg font-semibold mb-4">上传原图</h3>
                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center mb-4">
                                <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                                <p class="text-gray-500">拖拽图片到这里或点击上传</p>
                                <input type="file" class="hidden" accept="image/*">
                            </div>
                            
                            <textarea class="w-full h-24 p-4 border border-gray-300 rounded-lg resize-none" 
                                placeholder="描述你想要的变化，例如：将这张图片转换为油画风格"></textarea>
                            
                            <button class="w-full mt-4 bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 transition-colors">
                                <i class="fas fa-magic mr-2"></i>开始转换
                            </button>
                        </div>
                        
                        <div>
                            <h3 class="text-lg font-semibold mb-4">转换结果</h3>
                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                                <i class="fas fa-image text-4xl text-gray-400 mb-4"></i>
                                <p class="text-gray-500">转换后的图片将显示在这里</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 生成历史 -->
                <div class="mt-8">
                    <h3 class="text-lg font-semibold mb-4">生成历史</h3>
                    <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                        <div class="relative group cursor-pointer">
                            <img src="https://via.placeholder.com/150x150/FF6B6B/FFFFFF?text=History+1" alt="历史图片" class="rounded-lg">
                            <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                                <i class="fas fa-eye text-white text-xl"></i>
                            </div>
                        </div>
                        <div class="relative group cursor-pointer">
                            <img src="https://via.placeholder.com/150x150/4ECDC4/FFFFFF?text=History+2" alt="历史图片" class="rounded-lg">
                            <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                                <i class="fas fa-eye text-white text-xl"></i>
                            </div>
                        </div>
                        <div class="relative group cursor-pointer">
                            <img src="https://via.placeholder.com/150x150/45B7D1/FFFFFF?text=History+3" alt="历史图片" class="rounded-lg">
                            <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                                <i class="fas fa-eye text-white text-xl"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 视频展示 -->
        <div id="videos" class="tab-content">
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold flex items-center">
                        <i class="fas fa-video text-green-600 mr-3"></i>视频展示
                    </h2>
                    <div class="flex space-x-4">
                        <select class="px-4 py-2 border border-gray-300 rounded-lg">
                            <option>全部分类</option>
                            <option>技术教程</option>
                            <option>项目演示</option>
                            <option>生活记录</option>
                        </select>
                        <input type="text" placeholder="搜索视频..." class="px-4 py-2 border border-gray-300 rounded-lg">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="bg-gray-50 rounded-lg overflow-hidden card-hover">
                        <div class="relative">
                            <img src="https://via.placeholder.com/400x225/FF6B6B/FFFFFF?text=Video+Thumbnail" alt="视频缩略图" class="w-full h-48 object-cover">
                            <div class="absolute inset-0 flex items-center justify-center">
                                <button class="bg-white bg-opacity-80 rounded-full p-4 hover:bg-opacity-100 transition-all">
                                    <i class="fas fa-play text-2xl text-gray-800"></i>
                                </button>
                            </div>
                            <span class="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white px-2 py-1 rounded text-sm">10:25</span>
                        </div>
                        <div class="p-4">
                            <h3 class="font-semibold mb-2">使用Java 21构建现代Web应用</h3>
                            <p class="text-gray-600 text-sm mb-2">深入讲解Java 21的新特性以及如何在实际项目中应用</p>
                            <div class="flex items-center justify-between text-sm text-gray-500">
                                <span>2024-01-15</span>
                                <span><i class="fas fa-eye mr-1"></i>1.2K</span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gray-50 rounded-lg overflow-hidden card-hover">
                        <div class="relative">
                            <img src="https://via.placeholder.com/400x225/4ECDC4/FFFFFF?text=AI+Demo" alt="视频缩略图" class="w-full h-48 object-cover">
                            <div class="absolute inset-0 flex items-center justify-center">
                                <button class="bg-white bg-opacity-80 rounded-full p-4 hover:bg-opacity-100 transition-all">
                                    <i class="fas fa-play text-2xl text-gray-800"></i>
                                </button>
                            </div>
                            <span class="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white px-2 py-1 rounded text-sm">15:42</span>
                        </div>
                        <div class="p-4">
                            <h3 class="font-semibold mb-2">AI图片生成工具演示</h3>
                            <p class="text-gray-600 text-sm mb-2">展示如何使用AI工具生成高质量的图片内容</p>
                            <div class="flex items-center justify-between text-sm text-gray-500">
                                <span>2024-01-12</span>
                                <span><i class="fas fa-eye mr-1"></i>856</span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gray-50 rounded-lg overflow-hidden card-hover">
                        <div class="relative">
                            <img src="https://via.placeholder.com/400x225/45B7D1/FFFFFF?text=React+Tutorial" alt="视频缩略图" class="w-full h-48 object-cover">
                            <div class="absolute inset-0 flex items-center justify-center">
                                <button class="bg-white bg-opacity-80 rounded-full p-4 hover:bg-opacity-100 transition-all">
                                    <i class="fas fa-play text-2xl text-gray-800"></i>
                                </button>
                            </div>
                            <span class="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white px-2 py-1 rounded text-sm">22:18</span>
                        </div>
                        <div class="p-4">
                            <h3 class="font-semibold mb-2">React 18 Hooks深度解析</h3>
                            <p class="text-gray-600 text-sm mb-2">详细介绍React 18中的新Hooks及其使用场景</p>
                            <div class="flex items-center justify-between text-sm text-gray-500">
                                <span>2024-01-10</span>
                                <span><i class="fas fa-eye mr-1"></i>2.1K</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 分页 -->
                <div class="flex justify-center mt-8">
                    <nav class="flex space-x-2">
                        <button class="px-3 py-2 text-gray-500 hover:text-gray-700">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <button class="px-3 py-2 bg-blue-600 text-white rounded">1</button>
                        <button class="px-3 py-2 text-gray-700 hover:text-gray-900">2</button>
                        <button class="px-3 py-2 text-gray-700 hover:text-gray-900">3</button>
                        <button class="px-3 py-2 text-gray-500 hover:text-gray-700">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </nav>
                </div>
            </div>
        </div>

        <!-- 博客 -->
        <div id="blog" class="tab-content">
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold flex items-center">
                        <i class="fas fa-blog text-purple-600 mr-3"></i>技术博客
                    </h2>
                    <div class="flex space-x-4">
                        <select class="px-4 py-2 border border-gray-300 rounded-lg">
                            <option>全部分类</option>
                            <option>前端开发</option>
                            <option>后端开发</option>
                            <option>AI/机器学习</option>
                            <option>DevOps</option>
                        </select>
                        <input type="text" placeholder="搜索文章..." class="px-4 py-2 border border-gray-300 rounded-lg">
                    </div>
                </div>

                <div class="space-y-6">
                    <article class="border-l-4 border-blue-500 bg-gray-50 p-6 rounded-r-lg card-hover">
                        <div class="flex justify-between items-start mb-3">
                            <h3 class="text-xl font-semibold text-gray-900 hover:text-blue-600 cursor-pointer">
                                使用Vert.x构建高性能异步Web应用
                            </h3>
                            <span class="text-sm text-gray-500">2024-01-15</span>
                        </div>
                        <p class="text-gray-700 mb-4">
                            深入探讨如何使用Vert.x框架构建高性能的异步Web应用，包括事件循环机制、非阻塞I/O操作以及实际项目中的最佳实践...
                        </p>
                        <div class="flex items-center justify-between">
                            <div class="flex space-x-2">
                                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">Java</span>
                                <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">Vert.x</span>
                                <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-xs">异步编程</span>
                            </div>
                            <div class="flex items-center space-x-4 text-sm text-gray-500">
                                <span><i class="fas fa-eye mr-1"></i>1.2K</span>
                                <span><i class="fas fa-heart mr-1"></i>89</span>
                                <span><i class="fas fa-comment mr-1"></i>23</span>
                            </div>
                        </div>
                    </article>

                    <article class="border-l-4 border-green-500 bg-gray-50 p-6 rounded-r-lg card-hover">
                        <div class="flex justify-between items-start mb-3">
                            <h3 class="text-xl font-semibold text-gray-900 hover:text-blue-600 cursor-pointer">
                                AI图像生成技术深度解析与实践
                            </h3>
                            <span class="text-sm text-gray-500">2024-01-12</span>
                        </div>
                        <p class="text-gray-700 mb-4">
                            从Stable Diffusion到DALL-E，全面解析当前主流的AI图像生成技术原理，并通过实际代码演示如何集成这些服务...
                        </p>
                        <div class="flex items-center justify-between">
                            <div class="flex space-x-2">
                                <span class="bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs">AI</span>
                                <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs">机器学习</span>
                                <span class="bg-indigo-100 text-indigo-800 px-2 py-1 rounded-full text-xs">图像处理</span>
                            </div>
                            <div class="flex items-center space-x-4 text-sm text-gray-500">
                                <span><i class="fas fa-eye mr-1"></i>2.1K</span>
                                <span><i class="fas fa-heart mr-1"></i>156</span>
                                <span><i class="fas fa-comment mr-1"></i>45</span>
                            </div>
                        </div>
                    </article>

                    <article class="border-l-4 border-purple-500 bg-gray-50 p-6 rounded-r-lg card-hover">
                        <div class="flex justify-between items-start mb-3">
                            <h3 class="text-xl font-semibold text-gray-900 hover:text-blue-600 cursor-pointer">
                                React 18新特性实战指南
                            </h3>
                            <span class="text-sm text-gray-500">2024-01-10</span>
                        </div>
                        <p class="text-gray-700 mb-4">
                            详细介绍React 18中的并发特性、Suspense改进、自动批处理等新功能，并通过实际项目展示如何迁移和使用...
                        </p>
                        <div class="flex items-center justify-between">
                            <div class="flex space-x-2">
                                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">React</span>
                                <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">前端</span>
                                <span class="bg-orange-100 text-orange-800 px-2 py-1 rounded-full text-xs">JavaScript</span>
                            </div>
                            <div class="flex items-center space-x-4 text-sm text-gray-500">
                                <span><i class="fas fa-eye mr-1"></i>1.8K</span>
                                <span><i class="fas fa-heart mr-1"></i>124</span>
                                <span><i class="fas fa-comment mr-1"></i>67</span>
                            </div>
                        </div>
                    </article>
                </div>
            </div>
        </div>

        <!-- 项目展示 -->
        <div id="projects" class="tab-content">
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-2xl font-bold mb-6 flex items-center">
                    <i class="fas fa-code text-orange-600 mr-3"></i>项目展示
                </h2>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="bg-gray-50 rounded-lg p-6 card-hover">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold">SuperBlog</h3>
                            <div class="flex space-x-2">
                                <a href="#" class="text-gray-600 hover:text-blue-600">
                                    <i class="fab fa-github text-xl"></i>
                                </a>
                                <a href="#" class="text-gray-600 hover:text-green-600">
                                    <i class="fas fa-external-link-alt text-xl"></i>
                                </a>
                            </div>
                        </div>
                        <p class="text-gray-700 mb-4">个人技术展示平台，集成AI图片生成、视频展示、博客等功能的综合性网站。</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs">Java 21</span>
                            <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">Vert.x</span>
                            <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">React</span>
                            <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-xs">Next.js</span>
                        </div>
                        <div class="flex items-center text-sm text-gray-500">
                            <i class="fas fa-star mr-1"></i>
                            <span class="mr-4">128</span>
                            <i class="fas fa-code-branch mr-1"></i>
                            <span>23</span>
                        </div>
                    </div>

                    <div class="bg-gray-50 rounded-lg p-6 card-hover">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold">AI Image Generator</h3>
                            <div class="flex space-x-2">
                                <a href="#" class="text-gray-600 hover:text-blue-600">
                                    <i class="fab fa-github text-xl"></i>
                                </a>
                                <a href="#" class="text-gray-600 hover:text-green-600">
                                    <i class="fas fa-external-link-alt text-xl"></i>
                                </a>
                            </div>
                        </div>
                        <p class="text-gray-700 mb-4">基于Stable Diffusion的AI图片生成工具，支持文生图和图生图功能。</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs">Python</span>
                            <span class="bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs">FastAPI</span>
                            <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">PyTorch</span>
                            <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">Diffusers</span>
                        </div>
                        <div class="flex items-center text-sm text-gray-500">
                            <i class="fas fa-star mr-1"></i>
                            <span class="mr-4">89</span>
                            <i class="fas fa-code-branch mr-1"></i>
                            <span>15</span>
                        </div>
                    </div>

                    <div class="bg-gray-50 rounded-lg p-6 card-hover">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold">Video Platform</h3>
                            <div class="flex space-x-2">
                                <a href="#" class="text-gray-600 hover:text-blue-600">
                                    <i class="fab fa-github text-xl"></i>
                                </a>
                                <a href="#" class="text-gray-600 hover:text-green-600">
                                    <i class="fas fa-external-link-alt text-xl"></i>
                                </a>
                            </div>
                        </div>
                        <p class="text-gray-700 mb-4">现代化的视频分享平台，支持视频上传、转码、播放和管理功能。</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">Node.js</span>
                            <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">Express</span>
                            <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-xs">FFmpeg</span>
                            <span class="bg-orange-100 text-orange-800 px-2 py-1 rounded-full text-xs">MongoDB</span>
                        </div>
                        <div class="flex items-center text-sm text-gray-500">
                            <i class="fas fa-star mr-1"></i>
                            <span class="mr-4">67</span>
                            <i class="fas fa-code-branch mr-1"></i>
                            <span>12</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 小工具 -->
        <div id="tools" class="tab-content">
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-2xl font-bold mb-6 flex items-center">
                    <i class="fas fa-tools text-gray-600 mr-3"></i>实用小工具
                </h2>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-6 card-hover">
                        <div class="flex items-center mb-4">
                            <div class="bg-blue-600 text-white p-3 rounded-lg mr-4">
                                <i class="fas fa-code text-xl"></i>
                            </div>
                            <h3 class="text-lg font-semibold">JSON格式化</h3>
                        </div>
                        <p class="text-gray-700 mb-4">格式化和验证JSON数据，支持语法高亮和错误检测。</p>
                        <button class="w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700">
                            使用工具
                        </button>
                    </div>

                    <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-6 card-hover">
                        <div class="flex items-center mb-4">
                            <div class="bg-green-600 text-white p-3 rounded-lg mr-4">
                                <i class="fas fa-qrcode text-xl"></i>
                            </div>
                            <h3 class="text-lg font-semibold">二维码生成</h3>
                        </div>
                        <p class="text-gray-700 mb-4">快速生成各种类型的二维码，支持文本、URL、WiFi等。</p>
                        <button class="w-full bg-green-600 text-white py-2 rounded-lg hover:bg-green-700">
                            使用工具
                        </button>
                    </div>

                    <div class="bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-6 card-hover">
                        <div class="flex items-center mb-4">
                            <div class="bg-purple-600 text-white p-3 rounded-lg mr-4">
                                <i class="fas fa-key text-xl"></i>
                            </div>
                            <h3 class="text-lg font-semibold">密码生成器</h3>
                        </div>
                        <p class="text-gray-700 mb-4">生成安全的随机密码，可自定义长度和字符类型。</p>
                        <button class="w-full bg-purple-600 text-white py-2 rounded-lg hover:bg-purple-700">
                            使用工具
                        </button>
                    </div>

                    <div class="bg-gradient-to-br from-orange-50 to-orange-100 rounded-lg p-6 card-hover">
                        <div class="flex items-center mb-4">
                            <div class="bg-orange-600 text-white p-3 rounded-lg mr-4">
                                <i class="fas fa-clock text-xl"></i>
                            </div>
                            <h3 class="text-lg font-semibold">时间戳转换</h3>
                        </div>
                        <p class="text-gray-700 mb-4">Unix时间戳与日期时间的相互转换工具。</p>
                        <button class="w-full bg-orange-600 text-white py-2 rounded-lg hover:bg-orange-700">
                            使用工具
                        </button>
                    </div>

                    <div class="bg-gradient-to-br from-red-50 to-red-100 rounded-lg p-6 card-hover">
                        <div class="flex items-center mb-4">
                            <div class="bg-red-600 text-white p-3 rounded-lg mr-4">
                                <i class="fas fa-palette text-xl"></i>
                            </div>
                            <h3 class="text-lg font-semibold">颜色选择器</h3>
                        </div>
                        <p class="text-gray-700 mb-4">颜色选择和格式转换工具，支持HEX、RGB、HSL等。</p>
                        <button class="w-full bg-red-600 text-white py-2 rounded-lg hover:bg-red-700">
                            使用工具
                        </button>
                    </div>

                    <div class="bg-gradient-to-br from-indigo-50 to-indigo-100 rounded-lg p-6 card-hover">
                        <div class="flex items-center mb-4">
                            <div class="bg-indigo-600 text-white p-3 rounded-lg mr-4">
                                <i class="fas fa-exchange-alt text-xl"></i>
                            </div>
                            <h3 class="text-lg font-semibold">Base64编解码</h3>
                        </div>
                        <p class="text-gray-700 mb-4">Base64编码和解码工具，支持文本和文件处理。</p>
                        <button class="w-full bg-indigo-600 text-white py-2 rounded-lg hover:bg-indigo-700">
                            使用工具
                        </button>
                    </div>
                </div>
            </div>
        </div>

    </main>

    <!-- JavaScript -->
    <script>
        function showTab(tabName) {
            // 隐藏所有tab内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });

            // 移除所有tab按钮的active状态
            const tabButtons = document.querySelectorAll('.tab-btn');
            tabButtons.forEach(btn => {
                btn.classList.remove('active', 'text-blue-600');
                btn.classList.add('text-gray-700');
            });

            // 显示选中的tab内容
            document.getElementById(tabName).classList.add('active');

            // 设置选中的tab按钮为active状态
            event.target.classList.add('active', 'text-blue-600');
            event.target.classList.remove('text-gray-700');
        }

        function showAITool(toolName) {
            // 隐藏所有AI工具内容
            const toolContents = document.querySelectorAll('.ai-tool-content');
            toolContents.forEach(content => {
                content.style.display = 'none';
            });

            // 移除所有AI工具按钮的active状态
            const toolButtons = document.querySelectorAll('.ai-tool-btn');
            toolButtons.forEach(btn => {
                btn.classList.remove('bg-blue-600', 'text-white');
                btn.classList.add('bg-gray-200', 'text-gray-700');
            });

            // 显示选中的AI工具内容
            document.getElementById(toolName).style.display = 'block';

            // 设置选中的AI工具按钮为active状态
            event.target.classList.add('bg-blue-600', 'text-white');
            event.target.classList.remove('bg-gray-200', 'text-gray-700');
        }

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            // 设置默认选中的tab
            const homeTab = document.querySelector('[onclick="showTab(\'home\')"]');
            homeTab.classList.add('active', 'text-blue-600');
            homeTab.classList.remove('text-gray-700');
        });
    </script>
</body>
</html>
