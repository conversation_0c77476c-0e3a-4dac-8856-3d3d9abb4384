package com.superblog.infrastructure.external.ai;

import com.google.inject.Inject;
import com.superblog.config.AiConfig;
import com.superblog.domain.model.ImageGeneration;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.Vertx;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.client.WebClient;
import io.vertx.ext.web.client.WebClientOptions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * OpenAI API客户端
 * 负责与OpenAI服务进行交互
 */
public class OpenAiClient {

    private static final Logger logger = LoggerFactory.getLogger(OpenAiClient.class);

    private final WebClient webClient;
    private final AiConfig.OpenAiConfig config;

    @Inject
    public OpenAiClient(Vertx vertx, AiConfig aiConfig) {
        this.config = aiConfig.openai();
        
        WebClientOptions options = new WebClientOptions()
            .setDefaultHost(extractHost(config.baseUrl()))
            .setDefaultPort(extractPort(config.baseUrl()))
            .setSsl(config.baseUrl().startsWith("https"))
            .setTrustAll(true)
            .setTimeout(config.timeout());

        this.webClient = WebClient.create(vertx, options);
    }

    /**
     * 生成图片
     */
    public Future<String> generateImage(ImageGeneration generation) {
        Promise<String> promise = Promise.promise();

        if (config.apiKey() == null || config.apiKey().isEmpty()) {
            promise.fail(new RuntimeException("OpenAI API Key未配置"));
            return promise.future();
        }

        logger.info("调用OpenAI生成图片，提示词: {}", generation.getPrompt());

        JsonObject requestBody = new JsonObject()
            .put("model", generation.getModel() != null ? generation.getModel() : "dall-e-3")
            .put("prompt", generation.getPrompt())
            .put("n", 1)
            .put("size", generation.getWidth() + "x" + generation.getHeight())
            .put("quality", "standard")
            .put("response_format", "url");

        webClient
            .post("/v1/images/generations")
            .putHeader("Authorization", "Bearer " + config.apiKey())
            .putHeader("Content-Type", "application/json")
            .sendJsonObject(requestBody)
            .onSuccess(response -> {
                if (response.statusCode() == 200) {
                    try {
                        JsonObject responseBody = response.bodyAsJsonObject();
                        String imageUrl = responseBody
                            .getJsonArray("data")
                            .getJsonObject(0)
                            .getString("url");
                        
                        logger.info("OpenAI图片生成成功，URL: {}", imageUrl);
                        promise.complete(imageUrl);
                    } catch (Exception e) {
                        logger.error("解析OpenAI响应失败", e);
                        promise.fail(new RuntimeException("解析OpenAI响应失败: " + e.getMessage()));
                    }
                } else {
                    String errorMessage = "OpenAI API调用失败，状态码: " + response.statusCode();
                    if (response.body() != null) {
                        errorMessage += ", 响应: " + response.bodyAsString();
                    }
                    logger.error(errorMessage);
                    promise.fail(new RuntimeException(errorMessage));
                }
            })
            .onFailure(throwable -> {
                logger.error("调用OpenAI API失败", throwable);
                promise.fail(new RuntimeException("调用OpenAI API失败: " + throwable.getMessage()));
            });

        return promise.future();
    }

    /**
     * 从URL中提取主机名
     */
    private String extractHost(String url) {
        try {
            return url.replaceAll("^https?://", "").split("/")[0].split(":")[0];
        } catch (Exception e) {
            return "api.openai.com";
        }
    }

    /**
     * 从URL中提取端口
     */
    private int extractPort(String url) {
        try {
            if (url.startsWith("https")) {
                return 443;
            } else if (url.startsWith("http")) {
                return 80;
            }
            return 443;
        } catch (Exception e) {
            return 443;
        }
    }
}
