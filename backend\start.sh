#!/bin/bash

# SuperBlog Backend 启动脚本

echo "=== SuperBlog Backend 启动脚本 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查Java版本
echo -e "\n${YELLOW}检查Java版本...${NC}"
if command -v java &> /dev/null; then
    JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2 | cut -d'.' -f1)
    echo "Java版本: $JAVA_VERSION"
    if [ "$JAVA_VERSION" -lt 21 ]; then
        echo -e "${RED}错误: 需要Java 21或更高版本${NC}"
        exit 1
    fi
else
    echo -e "${RED}错误: 未找到Java${NC}"
    exit 1
fi

# 检查Maven
echo -e "\n${YELLOW}检查Maven...${NC}"
if command -v mvn &> /dev/null; then
    MVN_VERSION=$(mvn -version | head -n 1)
    echo "Maven版本: $MVN_VERSION"
else
    echo -e "${RED}错误: 未找到Maven${NC}"
    exit 1
fi

# 检查PostgreSQL连接
echo -e "\n${YELLOW}检查PostgreSQL连接...${NC}"
if command -v psql &> /dev/null; then
    if psql -h localhost -U superblog -d superblog_dev -c "SELECT 1;" &> /dev/null; then
        echo -e "${GREEN}PostgreSQL连接正常${NC}"
    else
        echo -e "${YELLOW}警告: PostgreSQL连接失败，请确保数据库已启动并配置正确${NC}"
        echo "可以使用以下命令创建数据库:"
        echo "createdb superblog_dev"
        echo "createuser superblog"
    fi
else
    echo -e "${YELLOW}警告: 未找到psql命令${NC}"
fi

# 检查Redis连接
echo -e "\n${YELLOW}检查Redis连接...${NC}"
if command -v redis-cli &> /dev/null; then
    if redis-cli ping &> /dev/null; then
        echo -e "${GREEN}Redis连接正常${NC}"
    else
        echo -e "${YELLOW}警告: Redis连接失败，请确保Redis已启动${NC}"
    fi
else
    echo -e "${YELLOW}警告: 未找到redis-cli命令${NC}"
fi

# 设置环境变量
echo -e "\n${YELLOW}设置环境变量...${NC}"
export PROFILE=dev
export JAVA_OPTS="-Xmx512m -Xms256m"

# 检查API密钥
if [ -z "$OPENAI_API_KEY" ]; then
    echo -e "${YELLOW}警告: OPENAI_API_KEY 环境变量未设置${NC}"
fi

if [ -z "$STABILITY_API_KEY" ]; then
    echo -e "${YELLOW}警告: STABILITY_API_KEY 环境变量未设置${NC}"
fi

# 编译项目
echo -e "\n${YELLOW}编译项目...${NC}"
if mvn compile -q; then
    echo -e "${GREEN}编译成功${NC}"
else
    echo -e "${RED}编译失败${NC}"
    exit 1
fi

# 运行数据库迁移
echo -e "\n${YELLOW}运行数据库迁移...${NC}"
if mvn flyway:migrate -q; then
    echo -e "${GREEN}数据库迁移完成${NC}"
else
    echo -e "${YELLOW}数据库迁移失败，可能是数据库连接问题${NC}"
fi

# 启动应用
echo -e "\n${GREEN}启动SuperBlog Backend...${NC}"
echo "访问地址: http://localhost:8080"
echo "健康检查: http://localhost:8080/api/health"
echo "按 Ctrl+C 停止应用"
echo ""

# 使用Vert.x插件启动
mvn vertx:run
