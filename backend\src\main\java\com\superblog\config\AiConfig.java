package com.superblog.config;

/**
 * AI服务配置类
 * 支持从YAML配置文件中读取配置
 */
public record AiConfig(
    OpenAiConfig openai,
    StabilityAiConfig stability,
    QwenConfig qwen
) {

    /**
     * OpenAI配置
     */
    public record OpenAiConfig(
        String apiKey,
        String baseUrl,
        String model,
        Integer timeout
    ) {
        public OpenAiConfig {
            // 设置默认值
            if (baseUrl == null || baseUrl.isEmpty()) {
                baseUrl = "https://api.openai.com/v1";
            }
            if (model == null || model.isEmpty()) {
                model = "gpt-3.5-turbo";
            }
            if (timeout == null) {
                timeout = 60000;
            }
        }
    }

    /**
     * Stability AI配置
     */
    public record StabilityAiConfig(
        String apiKey,
        String baseUrl,
        Integer timeout
    ) {
        public StabilityAiConfig {
            // 设置默认值
            if (baseUrl == null || baseUrl.isEmpty()) {
                baseUrl = "https://api.stability.ai/v1";
            }
            if (timeout == null) {
                timeout = 120000;
            }
        }
    }

    /**
     * 通义千问配置
     */
    public record QwenConfig(
        String apiKey,
        String baseUrl,
        String model,
        Integer timeout
    ) {
        public QwenConfig {
            // 设置默认值
            if (baseUrl == null || baseUrl.isEmpty()) {
                baseUrl = "https://dashscope.aliyuncs.com/api/v1";
            }
            if (model == null || model.isEmpty()) {
                model = "qwen-turbo";
            }
            if (timeout == null) {
                timeout = 60000;
            }
        }
    }
}
