version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: superblog-postgres
    environment:
      POSTGRES_DB: superblog
      POSTGRES_USER: superblog
      POSTGRES_PASSWORD: superblog123
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - superblog-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U superblog -d superblog"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: superblog-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - superblog-network
    restart: unless-stopped
    healthcheck:
      test: ["<PERSON><PERSON>", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru

  # SuperBlog后端应用
  superblog-backend:
    build:
      context: ..
      dockerfile: dev-ops/Dockerfile
    container_name: superblog-backend
    environment:
      - JAVA_OPTS=-Xmx512m -Xms256m
      - PROFILE=prod
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=superblog
      - DB_USER=superblog
      - DB_PASSWORD=superblog123
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - STABILITY_API_KEY=${STABILITY_API_KEY}
    ports:
      - "8080:8080"
    volumes:
      - app_uploads:/app/uploads
      - app_logs:/app/logs
    networks:
      - superblog-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: superblog-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
      - app_uploads:/var/www/uploads
    networks:
      - superblog-network
    depends_on:
      - superblog-backend
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  app_uploads:
    driver: local
  app_logs:
    driver: local

networks:
  superblog-network:
    driver: bridge
