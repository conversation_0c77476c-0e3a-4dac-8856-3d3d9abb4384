-- 创建基础表结构
-- SuperBlog 个人技术展示平台数据库初始化脚本

-- 用户表
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱地址',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    bio TEXT COMMENT '个人简介',
    github_url VARCHAR(200) COMMENT 'GitHub链接',
    linkedin_url VARCHAR(200) COMMENT 'LinkedIn链接',
    website_url VARCHAR(200) COMMENT '个人网站链接',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    role VARCHAR(20) DEFAULT 'USER' COMMENT '用户角色：USER, ADMIN',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间'
);

-- 创建用户表索引
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_created_at ON users(created_at);

-- AI图片生成记录表
CREATE TABLE image_generations (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id) ON DELETE CASCADE COMMENT '用户ID',
    generation_type VARCHAR(20) NOT NULL COMMENT '生成类型：TEXT_TO_IMAGE, IMAGE_TO_IMAGE',
    prompt TEXT NOT NULL COMMENT '提示词',
    negative_prompt TEXT COMMENT '负面提示词',
    original_image_url VARCHAR(500) COMMENT '原始图片URL（图生图时使用）',
    generated_image_url VARCHAR(500) COMMENT '生成的图片URL',
    thumbnail_url VARCHAR(500) COMMENT '缩略图URL',
    width INTEGER DEFAULT 512 COMMENT '图片宽度',
    height INTEGER DEFAULT 512 COMMENT '图片高度',
    style VARCHAR(50) COMMENT '艺术风格',
    model VARCHAR(50) COMMENT '使用的AI模型',
    seed BIGINT COMMENT '随机种子',
    steps INTEGER COMMENT '生成步数',
    cfg_scale DECIMAL(3,1) COMMENT 'CFG比例',
    status VARCHAR(20) DEFAULT 'PENDING' COMMENT '状态：PENDING, PROCESSING, COMPLETED, FAILED',
    error_message TEXT COMMENT '错误信息',
    generation_time INTEGER COMMENT '生成耗时（秒）',
    is_public BOOLEAN DEFAULT TRUE COMMENT '是否公开',
    view_count INTEGER DEFAULT 0 COMMENT '查看次数',
    like_count INTEGER DEFAULT 0 COMMENT '点赞次数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间'
);

-- 创建AI图片生成记录表索引
CREATE INDEX idx_image_generations_user_id ON image_generations(user_id);
CREATE INDEX idx_image_generations_type ON image_generations(generation_type);
CREATE INDEX idx_image_generations_status ON image_generations(status);
CREATE INDEX idx_image_generations_created_at ON image_generations(created_at);
CREATE INDEX idx_image_generations_public ON image_generations(is_public);

-- 视频表
CREATE TABLE videos (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id) ON DELETE CASCADE COMMENT '用户ID',
    title VARCHAR(200) NOT NULL COMMENT '视频标题',
    description TEXT COMMENT '视频描述',
    video_url VARCHAR(500) NOT NULL COMMENT '视频文件URL',
    thumbnail_url VARCHAR(500) COMMENT '缩略图URL',
    duration INTEGER COMMENT '视频时长（秒）',
    file_size BIGINT COMMENT '文件大小（字节）',
    format VARCHAR(20) COMMENT '视频格式',
    resolution VARCHAR(20) COMMENT '分辨率',
    category VARCHAR(50) COMMENT '分类：技术教程, 项目演示, 生活记录',
    tags TEXT[] COMMENT '标签数组',
    is_external BOOLEAN DEFAULT FALSE COMMENT '是否外部链接',
    external_platform VARCHAR(50) COMMENT '外部平台：YouTube, Bilibili',
    external_id VARCHAR(100) COMMENT '外部平台视频ID',
    view_count INTEGER DEFAULT 0 COMMENT '观看次数',
    like_count INTEGER DEFAULT 0 COMMENT '点赞次数',
    comment_count INTEGER DEFAULT 0 COMMENT '评论次数',
    is_published BOOLEAN DEFAULT FALSE COMMENT '是否发布',
    published_at TIMESTAMP COMMENT '发布时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间'
);

-- 创建视频表索引
CREATE INDEX idx_videos_user_id ON videos(user_id);
CREATE INDEX idx_videos_category ON videos(category);
CREATE INDEX idx_videos_published ON videos(is_published);
CREATE INDEX idx_videos_published_at ON videos(published_at);
CREATE INDEX idx_videos_created_at ON videos(created_at);
CREATE INDEX idx_videos_tags ON videos USING GIN(tags);

-- 文章分类表
CREATE TABLE categories (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE COMMENT '分类名称',
    description TEXT COMMENT '分类描述',
    color VARCHAR(7) DEFAULT '#3B82F6' COMMENT '分类颜色',
    sort_order INTEGER DEFAULT 0 COMMENT '排序顺序',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间'
);

-- 创建分类表索引
CREATE INDEX idx_categories_name ON categories(name);
CREATE INDEX idx_categories_active ON categories(is_active);
CREATE INDEX idx_categories_sort_order ON categories(sort_order);

-- 标签表
CREATE TABLE tags (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE COMMENT '标签名称',
    color VARCHAR(7) DEFAULT '#6B7280' COMMENT '标签颜色',
    usage_count INTEGER DEFAULT 0 COMMENT '使用次数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
);

-- 创建标签表索引
CREATE INDEX idx_tags_name ON tags(name);
CREATE INDEX idx_tags_usage_count ON tags(usage_count);

-- 文章表
CREATE TABLE articles (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id) ON DELETE CASCADE COMMENT '用户ID',
    category_id BIGINT REFERENCES categories(id) ON DELETE SET NULL COMMENT '分类ID',
    title VARCHAR(200) NOT NULL COMMENT '文章标题',
    summary TEXT COMMENT '文章摘要',
    content TEXT NOT NULL COMMENT '文章内容（Markdown格式）',
    cover_image_url VARCHAR(500) COMMENT '封面图片URL',
    slug VARCHAR(200) UNIQUE COMMENT 'URL别名',
    status VARCHAR(20) DEFAULT 'DRAFT' COMMENT '状态：DRAFT, PUBLISHED, ARCHIVED',
    is_featured BOOLEAN DEFAULT FALSE COMMENT '是否精选',
    view_count INTEGER DEFAULT 0 COMMENT '阅读次数',
    like_count INTEGER DEFAULT 0 COMMENT '点赞次数',
    comment_count INTEGER DEFAULT 0 COMMENT '评论次数',
    reading_time INTEGER COMMENT '预计阅读时间（分钟）',
    published_at TIMESTAMP COMMENT '发布时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间'
);

-- 创建文章表索引
CREATE INDEX idx_articles_user_id ON articles(user_id);
CREATE INDEX idx_articles_category_id ON articles(category_id);
CREATE INDEX idx_articles_status ON articles(status);
CREATE INDEX idx_articles_published_at ON articles(published_at);
CREATE INDEX idx_articles_created_at ON articles(created_at);
CREATE INDEX idx_articles_slug ON articles(slug);
CREATE INDEX idx_articles_featured ON articles(is_featured);

-- 文章标签关联表
CREATE TABLE article_tags (
    id BIGSERIAL PRIMARY KEY,
    article_id BIGINT REFERENCES articles(id) ON DELETE CASCADE COMMENT '文章ID',
    tag_id BIGINT REFERENCES tags(id) ON DELETE CASCADE COMMENT '标签ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE(article_id, tag_id)
);

-- 创建文章标签关联表索引
CREATE INDEX idx_article_tags_article_id ON article_tags(article_id);
CREATE INDEX idx_article_tags_tag_id ON article_tags(tag_id);

-- 项目表
CREATE TABLE projects (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id) ON DELETE CASCADE COMMENT '用户ID',
    name VARCHAR(100) NOT NULL COMMENT '项目名称',
    description TEXT COMMENT '项目描述',
    tech_stack TEXT[] COMMENT '技术栈数组',
    github_url VARCHAR(200) COMMENT 'GitHub链接',
    demo_url VARCHAR(200) COMMENT '演示链接',
    image_url VARCHAR(500) COMMENT '项目图片URL',
    star_count INTEGER DEFAULT 0 COMMENT 'GitHub Star数',
    fork_count INTEGER DEFAULT 0 COMMENT 'GitHub Fork数',
    language VARCHAR(50) COMMENT '主要编程语言',
    status VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE, ARCHIVED, PRIVATE',
    is_featured BOOLEAN DEFAULT FALSE COMMENT '是否精选',
    sort_order INTEGER DEFAULT 0 COMMENT '排序顺序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间'
);

-- 创建项目表索引
CREATE INDEX idx_projects_user_id ON projects(user_id);
CREATE INDEX idx_projects_status ON projects(status);
CREATE INDEX idx_projects_featured ON projects(is_featured);
CREATE INDEX idx_projects_sort_order ON projects(sort_order);
CREATE INDEX idx_projects_tech_stack ON projects USING GIN(tech_stack);

-- 评论表
CREATE TABLE comments (
    id BIGSERIAL PRIMARY KEY,
    article_id BIGINT REFERENCES articles(id) ON DELETE CASCADE COMMENT '文章ID',
    parent_id BIGINT REFERENCES comments(id) ON DELETE CASCADE COMMENT '父评论ID',
    author_name VARCHAR(50) NOT NULL COMMENT '评论者姓名',
    author_email VARCHAR(100) NOT NULL COMMENT '评论者邮箱',
    author_website VARCHAR(200) COMMENT '评论者网站',
    content TEXT NOT NULL COMMENT '评论内容',
    ip_address INET COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    status VARCHAR(20) DEFAULT 'PENDING' COMMENT '状态：PENDING, APPROVED, REJECTED, SPAM',
    like_count INTEGER DEFAULT 0 COMMENT '点赞次数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间'
);

-- 创建评论表索引
CREATE INDEX idx_comments_article_id ON comments(article_id);
CREATE INDEX idx_comments_parent_id ON comments(parent_id);
CREATE INDEX idx_comments_status ON comments(status);
CREATE INDEX idx_comments_created_at ON comments(created_at);

-- 工具使用统计表
CREATE TABLE tool_usage (
    id BIGSERIAL PRIMARY KEY,
    tool_name VARCHAR(50) NOT NULL COMMENT '工具名称',
    usage_count INTEGER DEFAULT 0 COMMENT '使用次数',
    last_used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '最后使用时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE(tool_name)
);

-- 创建工具使用统计表索引
CREATE INDEX idx_tool_usage_tool_name ON tool_usage(tool_name);
CREATE INDEX idx_tool_usage_count ON tool_usage(usage_count);

-- 访问统计表
CREATE TABLE analytics (
    id BIGSERIAL PRIMARY KEY,
    page_path VARCHAR(200) NOT NULL COMMENT '页面路径',
    ip_address INET COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    referer VARCHAR(500) COMMENT '来源页面',
    country VARCHAR(50) COMMENT '国家',
    city VARCHAR(50) COMMENT '城市',
    device_type VARCHAR(20) COMMENT '设备类型：desktop, mobile, tablet',
    browser VARCHAR(50) COMMENT '浏览器',
    os VARCHAR(50) COMMENT '操作系统',
    session_id VARCHAR(100) COMMENT '会话ID',
    visit_duration INTEGER COMMENT '访问时长（秒）',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '访问时间'
);

-- 创建访问统计表索引
CREATE INDEX idx_analytics_page_path ON analytics(page_path);
CREATE INDEX idx_analytics_ip_address ON analytics(ip_address);
CREATE INDEX idx_analytics_created_at ON analytics(created_at);
CREATE INDEX idx_analytics_session_id ON analytics(session_id);

-- 插入初始数据
INSERT INTO categories (name, description, color, sort_order) VALUES
('前端开发', '前端技术相关文章', '#3B82F6', 1),
('后端开发', '后端技术相关文章', '#10B981', 2),
('AI/机器学习', 'AI和机器学习相关文章', '#F59E0B', 3),
('DevOps', '运维和部署相关文章', '#EF4444', 4),
('数据库', '数据库相关文章', '#8B5CF6', 5),
('架构设计', '系统架构设计相关文章', '#06B6D4', 6);

INSERT INTO tags (name, color) VALUES
('Java', '#ED8936'),
('JavaScript', '#F6E05E'),
('React', '#4299E1'),
('Vue.js', '#48BB78'),
('Node.js', '#68D391'),
('Python', '#4299E1'),
('Go', '#4299E1'),
('Docker', '#4299E1'),
('Kubernetes', '#4299E1'),
('MySQL', '#ED8936'),
('PostgreSQL', '#4299E1'),
('Redis', '#E53E3E'),
('MongoDB', '#48BB78'),
('AWS', '#ED8936'),
('微服务', '#805AD5'),
('分布式', '#805AD5');

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表创建更新时间触发器
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_image_generations_updated_at BEFORE UPDATE ON image_generations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_videos_updated_at BEFORE UPDATE ON videos FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_categories_updated_at BEFORE UPDATE ON categories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_articles_updated_at BEFORE UPDATE ON articles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_comments_updated_at BEFORE UPDATE ON comments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tool_usage_updated_at BEFORE UPDATE ON tool_usage FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
