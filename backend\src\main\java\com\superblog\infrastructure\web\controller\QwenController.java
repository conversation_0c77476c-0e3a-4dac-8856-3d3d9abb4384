package com.superblog.infrastructure.web.controller;

import com.google.inject.Inject;
import com.superblog.infrastructure.external.ai.QwenClient;
import com.superblog.infrastructure.web.util.ApiResponse;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 通义千问AI控制器
 * 提供AI文本生成相关的API接口
 */
public class QwenController {

    private static final Logger logger = LoggerFactory.getLogger(QwenController.class);

    private final QwenClient qwenClient;

    @Inject
    public QwenController(QwenClient qwenClient) {
        this.qwenClient = qwenClient;
    }

    /**
     * 智能问答
     * POST /api/ai/chat
     */
    public void chat(RoutingContext context) {
        try {
            JsonObject requestBody = context.body().asJsonObject();
            String question = requestBody.getString("question");
            String contextInfo = requestBody.getString("context");

            if (question == null || question.trim().isEmpty()) {
                ApiResponse.badRequest(context, "问题不能为空");
                return;
            }

            logger.info("收到智能问答请求: {}", question);

            qwenClient.chat(question, contextInfo)
                .onSuccess(answer -> {
                    JsonObject responseData = new JsonObject()
                        .put("question", question)
                        .put("answer", answer)
                        .put("timestamp", System.currentTimeMillis());

                    ApiResponse.success(context, "问答成功", responseData);
                })
                .onFailure(throwable -> {
                    logger.error("智能问答失败", throwable);
                    ApiResponse.error(context, "智能问答失败: " + throwable.getMessage());
                });

        } catch (Exception e) {
            logger.error("处理智能问答请求时发生错误", e);
            ApiResponse.error(context, "请求处理失败");
        }
    }

    /**
     * 生成博客文章
     * POST /api/ai/generate-article
     */
    public void generateArticle(RoutingContext context) {
        try {
            JsonObject requestBody = context.body().asJsonObject();
            String topic = requestBody.getString("topic");
            String outline = requestBody.getString("outline");

            if (topic == null || topic.trim().isEmpty()) {
                ApiResponse.badRequest(context, "文章主题不能为空");
                return;
            }

            logger.info("收到生成文章请求，主题: {}", topic);

            qwenClient.generateBlogArticle(topic, outline)
                .onSuccess(article -> {
                    JsonObject responseData = new JsonObject()
                        .put("topic", topic)
                        .put("outline", outline)
                        .put("article", article)
                        .put("wordCount", article.length())
                        .put("timestamp", System.currentTimeMillis());

                    ApiResponse.success(context, "文章生成成功", responseData);
                })
                .onFailure(throwable -> {
                    logger.error("生成文章失败", throwable);
                    ApiResponse.error(context, "生成文章失败: " + throwable.getMessage());
                });

        } catch (Exception e) {
            logger.error("处理生成文章请求时发生错误", e);
            ApiResponse.error(context, "请求处理失败");
        }
    }

    /**
     * 优化绘画提示词
     * POST /api/ai/optimize-prompt
     */
    public void optimizeImagePrompt(RoutingContext context) {
        try {
            JsonObject requestBody = context.body().asJsonObject();
            String userInput = requestBody.getString("input");

            if (userInput == null || userInput.trim().isEmpty()) {
                ApiResponse.badRequest(context, "输入内容不能为空");
                return;
            }

            logger.info("收到提示词优化请求: {}", userInput);

            qwenClient.generateImagePrompt(userInput)
                .onSuccess(optimizedPrompt -> {
                    JsonObject responseData = new JsonObject()
                        .put("originalInput", userInput)
                        .put("optimizedPrompt", optimizedPrompt)
                        .put("timestamp", System.currentTimeMillis());

                    ApiResponse.success(context, "提示词优化成功", responseData);
                })
                .onFailure(throwable -> {
                    logger.error("提示词优化失败", throwable);
                    ApiResponse.error(context, "提示词优化失败: " + throwable.getMessage());
                });

        } catch (Exception e) {
            logger.error("处理提示词优化请求时发生错误", e);
            ApiResponse.error(context, "请求处理失败");
        }
    }

    /**
     * 自由文本生成
     * POST /api/ai/generate-text
     */
    public void generateText(RoutingContext context) {
        try {
            JsonObject requestBody = context.body().asJsonObject();
            String prompt = requestBody.getString("prompt");
            String systemMessage = requestBody.getString("systemMessage");
            Integer maxTokens = requestBody.getInteger("maxTokens", 1000);
            Float temperature = requestBody.getFloat("temperature", 0.7f);

            if (prompt == null || prompt.trim().isEmpty()) {
                ApiResponse.badRequest(context, "提示词不能为空");
                return;
            }

            logger.info("收到文本生成请求: {}", prompt);

            qwenClient.generateText(prompt, systemMessage, maxTokens, temperature)
                .onSuccess(generatedText -> {
                    JsonObject responseData = new JsonObject()
                        .put("prompt", prompt)
                        .put("systemMessage", systemMessage)
                        .put("generatedText", generatedText)
                        .put("parameters", new JsonObject()
                            .put("maxTokens", maxTokens)
                            .put("temperature", temperature))
                        .put("timestamp", System.currentTimeMillis());

                    ApiResponse.success(context, "文本生成成功", responseData);
                })
                .onFailure(throwable -> {
                    logger.error("文本生成失败", throwable);
                    ApiResponse.error(context, "文本生成失败: " + throwable.getMessage());
                });

        } catch (Exception e) {
            logger.error("处理文本生成请求时发生错误", e);
            ApiResponse.error(context, "请求处理失败");
        }
    }

    /**
     * 测试API连接
     * GET /api/ai/qwen/test
     */
    public void testConnection(RoutingContext context) {
        logger.info("测试通义千问API连接");

        qwenClient.testConnection()
            .onSuccess(isConnected -> {
                JsonObject responseData = new JsonObject()
                    .put("connected", isConnected)
                    .put("timestamp", System.currentTimeMillis());

                if (isConnected) {
                    ApiResponse.success(context, "通义千问API连接正常", responseData);
                } else {
                    ApiResponse.error(context, "通义千问API连接失败", responseData);
                }
            })
            .onFailure(throwable -> {
                logger.error("测试通义千问API连接失败", throwable);
                ApiResponse.error(context, "连接测试失败: " + throwable.getMessage());
            });
    }
}
