package com.superblog.config;

/**
 * Redis配置类
 */
public record RedisConfig(
    String host,
    Integer port,
    String password,
    Integer database,
    Integer maxPoolSize,
    Integer maxWaitingHandlers
) {
    
    /**
     * 获取Redis连接字符串
     */
    public String getConnectionString() {
        if (password != null && !password.isEmpty()) {
            return String.format("redis://:%s@%s:%d/%d", password, host, port, database);
        } else {
            return String.format("redis://%s:%d/%d", host, port, database);
        }
    }
}
