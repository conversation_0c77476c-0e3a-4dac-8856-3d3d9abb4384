package com.superblog.infrastructure.web;

import com.google.inject.Inject;
import com.superblog.config.CorsConfig;
import com.superblog.infrastructure.web.controller.*;
import io.vertx.core.Vertx;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.handler.BodyHandler;
import io.vertx.ext.web.handler.CorsHandler;
import io.vertx.ext.web.handler.LoggerHandler;
import io.vertx.ext.web.handler.TimeoutHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 路由工厂类
 * 负责创建和配置应用程序的路由
 */
public class RouterFactory {

    private static final Logger logger = LoggerFactory.getLogger(RouterFactory.class);

    private final Vertx vertx;
    private final CorsConfig corsConfig;
    private final ImageGenerationController imageGenerationController;
    private final VideoController videoController;
    private final ArticleController articleController;
    private final ProjectController projectController;
    private final UserController userController;

    @Inject
    public RouterFactory(
            Vertx vertx,
            CorsConfig corsConfig,
            ImageGenerationController imageGenerationController,
            VideoController videoController,
            ArticleController articleController,
            ProjectController projectController,
            UserController userController) {
        this.vertx = vertx;
        this.corsConfig = corsConfig;
        this.imageGenerationController = imageGenerationController;
        this.videoController = videoController;
        this.articleController = articleController;
        this.projectController = projectController;
        this.userController = userController;
    }

    /**
     * 创建主路由
     */
    public Router createRouter() {
        Router router = Router.router(vertx);

        // 配置全局中间件
        configureGlobalMiddleware(router);

        // 配置API路由
        configureApiRoutes(router);

        // 配置静态资源路由
        configureStaticRoutes(router);

        // 配置错误处理
        configureErrorHandling(router);

        logger.info("路由配置完成");
        return router;
    }

    /**
     * 配置全局中间件
     */
    private void configureGlobalMiddleware(Router router) {
        // 请求日志
        router.route().handler(LoggerHandler.create());

        // 请求超时
        router.route().handler(TimeoutHandler.create(30000));

        // CORS处理
        CorsHandler corsHandler = CorsHandler.create();
        corsConfig.allowedOrigins().forEach(corsHandler::addOrigin);
        corsConfig.allowedMethods().forEach(method -> 
            corsHandler.addRelativeOrigin(method));
        corsConfig.allowedHeaders().forEach(corsHandler::addHeader);
        if (corsConfig.allowCredentials()) {
            corsHandler.allowCredentials(true);
        }
        router.route().handler(corsHandler);

        // 请求体处理
        router.route().handler(BodyHandler.create()
            .setBodyLimit(10 * 1024 * 1024) // 10MB
            .setMergeFormAttributes(true)
            .setDeleteUploadedFilesOnEnd(true));
    }

    /**
     * 配置API路由
     */
    private void configureApiRoutes(Router router) {
        // API基础路径
        Router apiRouter = Router.router(vertx);

        // 健康检查
        apiRouter.get("/health").handler(context -> {
            context.response()
                .putHeader("Content-Type", "application/json")
                .end("{\"status\":\"UP\",\"timestamp\":" + System.currentTimeMillis() + "}");
        });

        // AI图片生成相关路由
        configureImageGenerationRoutes(apiRouter);

        // 视频相关路由
        configureVideoRoutes(apiRouter);

        // 文章相关路由
        configureArticleRoutes(apiRouter);

        // 项目相关路由
        configureProjectRoutes(apiRouter);

        // 用户相关路由
        configureUserRoutes(apiRouter);

        // 挂载API路由
        router.mountSubRouter("/api", apiRouter);
    }

    /**
     * 配置AI图片生成路由
     */
    private void configureImageGenerationRoutes(Router router) {
        // 创建文生图任务
        router.post("/ai/text-to-image").handler(imageGenerationController::createTextToImage);

        // 创建图生图任务
        router.post("/ai/image-to-image").handler(imageGenerationController::createImageToImage);

        // 获取图片生成记录详情
        router.get("/ai/generations/:id").handler(imageGenerationController::getGenerationById);

        // 获取用户的图片生成记录列表
        router.get("/ai/generations/user/:userId").handler(imageGenerationController::getUserGenerations);

        // 获取公开的图片生成记录列表
        router.get("/ai/generations/public").handler(imageGenerationController::getPublicGenerations);

        // 获取热门图片生成记录
        router.get("/ai/generations/popular").handler(imageGenerationController::getPopularGenerations);

        // 获取最新图片生成记录
        router.get("/ai/generations/latest").handler(imageGenerationController::getLatestGenerations);

        // 搜索图片生成记录
        router.get("/ai/generations/search").handler(imageGenerationController::searchGenerations);

        // 点赞图片生成记录
        router.post("/ai/generations/:id/like").handler(imageGenerationController::likeGeneration);

        // 删除图片生成记录
        router.delete("/ai/generations/:id").handler(imageGenerationController::deleteGeneration);
    }

    /**
     * 配置视频路由
     */
    private void configureVideoRoutes(Router router) {
        // 获取视频列表
        router.get("/videos").handler(videoController::getVideos);

        // 获取视频详情
        router.get("/videos/:id").handler(videoController::getVideoById);

        // TODO: 添加更多视频相关路由
    }

    /**
     * 配置文章路由
     */
    private void configureArticleRoutes(Router router) {
        // 获取文章列表
        router.get("/articles").handler(articleController::getArticles);

        // 获取文章详情
        router.get("/articles/:id").handler(articleController::getArticleById);

        // TODO: 添加更多文章相关路由
    }

    /**
     * 配置项目路由
     */
    private void configureProjectRoutes(Router router) {
        // 获取项目列表
        router.get("/projects").handler(projectController::getProjects);

        // 获取项目详情
        router.get("/projects/:id").handler(projectController::getProjectById);

        // TODO: 添加更多项目相关路由
    }

    /**
     * 配置用户路由
     */
    private void configureUserRoutes(Router router) {
        // 获取用户资料
        router.get("/users/profile").handler(userController::getUserProfile);

        // 更新用户资料
        router.put("/users/profile").handler(userController::updateUserProfile);

        // TODO: 添加更多用户相关路由
    }

    /**
     * 配置静态资源路由
     */
    private void configureStaticRoutes(Router router) {
        // 静态文件服务
        router.route("/static/*").handler(context -> {
            String path = context.request().path();
            // TODO: 实现静态文件服务
            context.response()
                .setStatusCode(404)
                .end("Static file not found: " + path);
        });

        // 上传文件服务
        router.route("/uploads/*").handler(context -> {
            String path = context.request().path();
            // TODO: 实现上传文件服务
            context.response()
                .setStatusCode(404)
                .end("Upload file not found: " + path);
        });
    }

    /**
     * 配置错误处理
     */
    private void configureErrorHandling(Router router) {
        // 404处理
        router.route().last().handler(context -> {
            context.response()
                .setStatusCode(404)
                .putHeader("Content-Type", "application/json")
                .end("{\"success\":false,\"code\":404,\"message\":\"API接口不存在\",\"timestamp\":" + System.currentTimeMillis() + "}");
        });

        // 全局异常处理
        router.route().failureHandler(context -> {
            Throwable failure = context.failure();
            int statusCode = context.statusCode();
            
            if (statusCode == -1) {
                statusCode = 500;
            }

            String message = "服务器内部错误";
            if (failure != null) {
                message = failure.getMessage();
                logger.error("请求处理失败", failure);
            }

            context.response()
                .setStatusCode(statusCode)
                .putHeader("Content-Type", "application/json")
                .end("{\"success\":false,\"code\":" + statusCode + ",\"message\":\"" + message + "\",\"timestamp\":" + System.currentTimeMillis() + "}");
        });
    }
}
