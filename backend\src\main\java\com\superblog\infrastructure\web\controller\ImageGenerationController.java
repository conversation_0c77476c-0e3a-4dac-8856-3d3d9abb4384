package com.superblog.infrastructure.web.controller;

import com.google.inject.Inject;
import com.superblog.application.dto.ImageGenerationRequest;
import com.superblog.application.dto.ImageGenerationResponse;
import com.superblog.application.service.ImageGenerationService;
import com.superblog.infrastructure.web.response.ApiResponse;
import io.vertx.core.Future;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Optional;

/**
 * AI图片生成控制器
 * 处理图片生成相关的HTTP请求
 */
public class ImageGenerationController {

    private static final Logger logger = LoggerFactory.getLogger(ImageGenerationController.class);

    private final ImageGenerationService imageGenerationService;

    @Inject
    public ImageGenerationController(ImageGenerationService imageGenerationService) {
        this.imageGenerationService = imageGenerationService;
    }

    /**
     * 创建文生图任务
     * POST /api/ai/text-to-image
     */
    public void createTextToImage(RoutingContext context) {
        try {
            JsonObject requestBody = context.body().asJsonObject();
            ImageGenerationRequest request = requestBody.mapTo(ImageGenerationRequest.class);

            // 验证请求参数
            if (!request.isValidForTextToImage()) {
                ApiResponse.badRequest(context, "请求参数无效");
                return;
            }

            // TODO: 从JWT token中获取用户ID
            request.setUserId(1L); // 临时设置

            imageGenerationService.createTextToImageGeneration(request)
                .onSuccess(response -> {
                    logger.info("文生图任务创建成功，ID: {}", response.getId());
                    ApiResponse.success(context, response, "文生图任务创建成功");
                })
                .onFailure(throwable -> {
                    logger.error("创建文生图任务失败", throwable);
                    ApiResponse.internalError(context, "创建文生图任务失败: " + throwable.getMessage());
                });

        } catch (Exception e) {
            logger.error("处理文生图请求失败", e);
            ApiResponse.badRequest(context, "请求格式错误");
        }
    }

    /**
     * 创建图生图任务
     * POST /api/ai/image-to-image
     */
    public void createImageToImage(RoutingContext context) {
        try {
            JsonObject requestBody = context.body().asJsonObject();
            ImageGenerationRequest request = requestBody.mapTo(ImageGenerationRequest.class);

            // 验证请求参数
            if (!request.isValidForImageToImage()) {
                ApiResponse.badRequest(context, "请求参数无效");
                return;
            }

            // TODO: 从JWT token中获取用户ID
            request.setUserId(1L); // 临时设置

            imageGenerationService.createImageToImageGeneration(request)
                .onSuccess(response -> {
                    logger.info("图生图任务创建成功，ID: {}", response.getId());
                    ApiResponse.success(context, response, "图生图任务创建成功");
                })
                .onFailure(throwable -> {
                    logger.error("创建图生图任务失败", throwable);
                    ApiResponse.internalError(context, "创建图生图任务失败: " + throwable.getMessage());
                });

        } catch (Exception e) {
            logger.error("处理图生图请求失败", e);
            ApiResponse.badRequest(context, "请求格式错误");
        }
    }

    /**
     * 获取图片生成记录详情
     * GET /api/ai/generations/:id
     */
    public void getGenerationById(RoutingContext context) {
        try {
            String idParam = context.pathParam("id");
            Long id = Long.parseLong(idParam);

            imageGenerationService.getGenerationById(id)
                .onSuccess(optionalResponse -> {
                    if (optionalResponse.isPresent()) {
                        ImageGenerationResponse response = optionalResponse.get();
                        
                        // 增加查看次数
                        imageGenerationService.incrementViewCount(id);
                        
                        ApiResponse.success(context, response);
                    } else {
                        ApiResponse.notFound(context, "图片生成记录不存在");
                    }
                })
                .onFailure(throwable -> {
                    logger.error("获取图片生成记录失败，ID: {}", id, throwable);
                    ApiResponse.internalError(context, "获取图片生成记录失败");
                });

        } catch (NumberFormatException e) {
            ApiResponse.badRequest(context, "无效的ID格式");
        } catch (Exception e) {
            logger.error("处理获取图片生成记录请求失败", e);
            ApiResponse.internalError(context, "服务器内部错误");
        }
    }

    /**
     * 获取用户的图片生成记录列表
     * GET /api/ai/generations/user/:userId
     */
    public void getUserGenerations(RoutingContext context) {
        try {
            String userIdParam = context.pathParam("userId");
            Long userId = Long.parseLong(userIdParam);

            int page = Integer.parseInt(context.request().getParam("page", "0"));
            int size = Integer.parseInt(context.request().getParam("size", "20"));

            // 限制分页大小
            if (size > 100) {
                size = 100;
            }

            imageGenerationService.getUserGenerations(userId, page, size)
                .onSuccess(responses -> {
                    ApiResponse.success(context, responses);
                })
                .onFailure(throwable -> {
                    logger.error("获取用户图片生成记录失败，用户ID: {}", userId, throwable);
                    ApiResponse.internalError(context, "获取用户图片生成记录失败");
                });

        } catch (NumberFormatException e) {
            ApiResponse.badRequest(context, "无效的参数格式");
        } catch (Exception e) {
            logger.error("处理获取用户图片生成记录请求失败", e);
            ApiResponse.internalError(context, "服务器内部错误");
        }
    }

    /**
     * 获取公开的图片生成记录列表
     * GET /api/ai/generations/public
     */
    public void getPublicGenerations(RoutingContext context) {
        try {
            int page = Integer.parseInt(context.request().getParam("page", "0"));
            int size = Integer.parseInt(context.request().getParam("size", "20"));

            // 限制分页大小
            if (size > 100) {
                size = 100;
            }

            imageGenerationService.getPublicGenerations(page, size)
                .onSuccess(responses -> {
                    ApiResponse.success(context, responses);
                })
                .onFailure(throwable -> {
                    logger.error("获取公开图片生成记录失败", throwable);
                    ApiResponse.internalError(context, "获取公开图片生成记录失败");
                });

        } catch (NumberFormatException e) {
            ApiResponse.badRequest(context, "无效的参数格式");
        } catch (Exception e) {
            logger.error("处理获取公开图片生成记录请求失败", e);
            ApiResponse.internalError(context, "服务器内部错误");
        }
    }

    /**
     * 获取热门图片生成记录
     * GET /api/ai/generations/popular
     */
    public void getPopularGenerations(RoutingContext context) {
        try {
            int limit = Integer.parseInt(context.request().getParam("limit", "10"));

            // 限制数量
            if (limit > 50) {
                limit = 50;
            }

            imageGenerationService.getPopularGenerations(limit)
                .onSuccess(responses -> {
                    ApiResponse.success(context, responses);
                })
                .onFailure(throwable -> {
                    logger.error("获取热门图片生成记录失败", throwable);
                    ApiResponse.internalError(context, "获取热门图片生成记录失败");
                });

        } catch (NumberFormatException e) {
            ApiResponse.badRequest(context, "无效的参数格式");
        } catch (Exception e) {
            logger.error("处理获取热门图片生成记录请求失败", e);
            ApiResponse.internalError(context, "服务器内部错误");
        }
    }

    /**
     * 获取最新图片生成记录
     * GET /api/ai/generations/latest
     */
    public void getLatestGenerations(RoutingContext context) {
        try {
            int limit = Integer.parseInt(context.request().getParam("limit", "10"));

            // 限制数量
            if (limit > 50) {
                limit = 50;
            }

            imageGenerationService.getLatestGenerations(limit)
                .onSuccess(responses -> {
                    ApiResponse.success(context, responses);
                })
                .onFailure(throwable -> {
                    logger.error("获取最新图片生成记录失败", throwable);
                    ApiResponse.internalError(context, "获取最新图片生成记录失败");
                });

        } catch (NumberFormatException e) {
            ApiResponse.badRequest(context, "无效的参数格式");
        } catch (Exception e) {
            logger.error("处理获取最新图片生成记录请求失败", e);
            ApiResponse.internalError(context, "服务器内部错误");
        }
    }

    /**
     * 搜索图片生成记录
     * GET /api/ai/generations/search
     */
    public void searchGenerations(RoutingContext context) {
        try {
            String keyword = context.request().getParam("keyword");
            if (keyword == null || keyword.trim().isEmpty()) {
                ApiResponse.badRequest(context, "搜索关键词不能为空");
                return;
            }

            int page = Integer.parseInt(context.request().getParam("page", "0"));
            int size = Integer.parseInt(context.request().getParam("size", "20"));

            // 限制分页大小
            if (size > 100) {
                size = 100;
            }

            imageGenerationService.searchGenerations(keyword.trim(), page, size)
                .onSuccess(responses -> {
                    ApiResponse.success(context, responses);
                })
                .onFailure(throwable -> {
                    logger.error("搜索图片生成记录失败，关键词: {}", keyword, throwable);
                    ApiResponse.internalError(context, "搜索图片生成记录失败");
                });

        } catch (NumberFormatException e) {
            ApiResponse.badRequest(context, "无效的参数格式");
        } catch (Exception e) {
            logger.error("处理搜索图片生成记录请求失败", e);
            ApiResponse.internalError(context, "服务器内部错误");
        }
    }

    /**
     * 点赞图片生成记录
     * POST /api/ai/generations/:id/like
     */
    public void likeGeneration(RoutingContext context) {
        try {
            String idParam = context.pathParam("id");
            Long id = Long.parseLong(idParam);

            imageGenerationService.incrementLikeCount(id)
                .onSuccess(v -> {
                    ApiResponse.success(context, null, "点赞成功");
                })
                .onFailure(throwable -> {
                    logger.error("点赞图片生成记录失败，ID: {}", id, throwable);
                    ApiResponse.internalError(context, "点赞失败");
                });

        } catch (NumberFormatException e) {
            ApiResponse.badRequest(context, "无效的ID格式");
        } catch (Exception e) {
            logger.error("处理点赞请求失败", e);
            ApiResponse.internalError(context, "服务器内部错误");
        }
    }

    /**
     * 删除图片生成记录
     * DELETE /api/ai/generations/:id
     */
    public void deleteGeneration(RoutingContext context) {
        try {
            String idParam = context.pathParam("id");
            Long id = Long.parseLong(idParam);

            // TODO: 从JWT token中获取用户ID
            Long userId = 1L; // 临时设置

            imageGenerationService.deleteGeneration(id, userId)
                .onSuccess(deleted -> {
                    if (deleted) {
                        ApiResponse.success(context, null, "删除成功");
                    } else {
                        ApiResponse.notFound(context, "图片生成记录不存在");
                    }
                })
                .onFailure(throwable -> {
                    logger.error("删除图片生成记录失败，ID: {}", id, throwable);
                    if (throwable.getMessage().contains("无权限")) {
                        ApiResponse.forbidden(context, throwable.getMessage());
                    } else {
                        ApiResponse.internalError(context, "删除失败");
                    }
                });

        } catch (NumberFormatException e) {
            ApiResponse.badRequest(context, "无效的ID格式");
        } catch (Exception e) {
            logger.error("处理删除请求失败", e);
            ApiResponse.internalError(context, "服务器内部错误");
        }
    }
}
