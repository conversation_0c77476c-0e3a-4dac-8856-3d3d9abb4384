package com.superblog.infrastructure.external.ai;

import com.google.inject.Inject;
import com.superblog.config.AiConfig;
import com.superblog.domain.model.ImageGeneration;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.Vertx;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.client.WebClient;
import io.vertx.ext.web.client.WebClientOptions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Stability AI API客户端
 * 负责与Stability AI服务进行交互
 */
public class StabilityAiClient {

    private static final Logger logger = LoggerFactory.getLogger(StabilityAiClient.class);

    private final WebClient webClient;
    private final AiConfig.StabilityAiConfig config;

    @Inject
    public StabilityAiClient(Vertx vertx, AiConfig aiConfig) {
        this.config = aiConfig.stabilityai();
        
        WebClientOptions options = new WebClientOptions()
            .setDefaultHost(extractHost(config.baseUrl()))
            .setDefaultPort(extractPort(config.baseUrl()))
            .setSsl(config.baseUrl().startsWith("https"))
            .setTrustAll(true)
            .setTimeout(config.timeout());

        this.webClient = WebClient.create(vertx, options);
    }

    /**
     * 生成图片（文生图）
     */
    public Future<String> generateImage(ImageGeneration generation) {
        Promise<String> promise = Promise.promise();

        if (config.apiKey() == null || config.apiKey().isEmpty()) {
            promise.fail(new RuntimeException("Stability AI API Key未配置"));
            return promise.future();
        }

        logger.info("调用Stability AI生成图片，提示词: {}", generation.getPrompt());

        JsonObject requestBody = new JsonObject()
            .put("text_prompts", new JsonArray()
                .add(new JsonObject()
                    .put("text", generation.getPrompt())
                    .put("weight", 1.0)
                )
            )
            .put("cfg_scale", generation.getCfgScale() != null ? generation.getCfgScale() : 7.0)
            .put("height", generation.getHeight() != null ? generation.getHeight() : 512)
            .put("width", generation.getWidth() != null ? generation.getWidth() : 512)
            .put("samples", 1)
            .put("steps", generation.getSteps() != null ? generation.getSteps() : 20);

        if (generation.getNegativePrompt() != null && !generation.getNegativePrompt().isEmpty()) {
            requestBody.getJsonArray("text_prompts")
                .add(new JsonObject()
                    .put("text", generation.getNegativePrompt())
                    .put("weight", -1.0)
                );
        }

        if (generation.getSeed() != null) {
            requestBody.put("seed", generation.getSeed());
        }

        webClient
            .post("/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image")
            .putHeader("Authorization", "Bearer " + config.apiKey())
            .putHeader("Content-Type", "application/json")
            .putHeader("Accept", "application/json")
            .sendJsonObject(requestBody)
            .onSuccess(response -> {
                if (response.statusCode() == 200) {
                    try {
                        JsonObject responseBody = response.bodyAsJsonObject();
                        String base64Image = responseBody
                            .getJsonArray("artifacts")
                            .getJsonObject(0)
                            .getString("base64");
                        
                        // TODO: 将base64图片保存到文件系统或云存储，返回URL
                        String imageUrl = saveBase64Image(base64Image, generation.getId());
                        
                        logger.info("Stability AI图片生成成功，URL: {}", imageUrl);
                        promise.complete(imageUrl);
                    } catch (Exception e) {
                        logger.error("解析Stability AI响应失败", e);
                        promise.fail(new RuntimeException("解析Stability AI响应失败: " + e.getMessage()));
                    }
                } else {
                    String errorMessage = "Stability AI API调用失败，状态码: " + response.statusCode();
                    if (response.body() != null) {
                        errorMessage += ", 响应: " + response.bodyAsString();
                    }
                    logger.error(errorMessage);
                    promise.fail(new RuntimeException(errorMessage));
                }
            })
            .onFailure(throwable -> {
                logger.error("调用Stability AI API失败", throwable);
                promise.fail(new RuntimeException("调用Stability AI API失败: " + throwable.getMessage()));
            });

        return promise.future();
    }

    /**
     * 图生图
     */
    public Future<String> generateImageFromImage(ImageGeneration generation) {
        Promise<String> promise = Promise.promise();

        if (config.apiKey() == null || config.apiKey().isEmpty()) {
            promise.fail(new RuntimeException("Stability AI API Key未配置"));
            return promise.future();
        }

        logger.info("调用Stability AI图生图，原图URL: {}", generation.getOriginalImageUrl());

        // TODO: 实现图生图功能
        // 需要先下载原图，转换为base64，然后调用image-to-image API
        
        promise.fail(new RuntimeException("图生图功能暂未实现"));
        return promise.future();
    }

    /**
     * 保存base64图片到文件系统
     * TODO: 实现图片保存逻辑
     */
    private String saveBase64Image(String base64Image, Long generationId) {
        // 临时实现：返回一个模拟的URL
        return "https://example.com/generated-images/" + generationId + ".png";
    }

    /**
     * 从URL中提取主机名
     */
    private String extractHost(String url) {
        try {
            return url.replaceAll("^https?://", "").split("/")[0].split(":")[0];
        } catch (Exception e) {
            return "api.stability.ai";
        }
    }

    /**
     * 从URL中提取端口
     */
    private int extractPort(String url) {
        try {
            if (url.startsWith("https")) {
                return 443;
            } else if (url.startsWith("http")) {
                return 80;
            }
            return 443;
        } catch (Exception e) {
            return 443;
        }
    }
}
