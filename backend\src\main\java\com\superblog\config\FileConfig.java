package com.superblog.config;

/**
 * 文件配置类
 * 支持从YAML配置文件中读取配置
 */
public record FileConfig(
    UploadConfig upload
) {
    
    /**
     * 文件上传配置
     */
    public record UploadConfig(
        String path,
        String maxSize,
        String allowedTypes
    ) {
        public UploadConfig {
            // 设置默认值
            if (path == null || path.isEmpty()) {
                path = "./uploads";
            }
            if (maxSize == null || maxSize.isEmpty()) {
                maxSize = "10MB";
            }
            if (allowedTypes == null || allowedTypes.isEmpty()) {
                allowedTypes = "jpg,jpeg,png,gif,webp";
            }
        }
        
        /**
         * 检查文件类型是否允许
         */
        public boolean isAllowedType(String fileExtension) {
            if (fileExtension == null || allowedTypes == null) {
                return false;
            }
            String lowerExt = fileExtension.toLowerCase();
            return allowedTypes.toLowerCase().contains(lowerExt);
        }
        
        /**
         * 获取最大文件大小（字节）
         */
        public long getMaxSizeInBytes() {
            if (maxSize == null) return 10 * 1024 * 1024; // 默认10MB
            
            String size = maxSize.toUpperCase();
            if (size.endsWith("KB")) {
                return Long.parseLong(size.substring(0, size.length() - 2)) * 1024;
            } else if (size.endsWith("MB")) {
                return Long.parseLong(size.substring(0, size.length() - 2)) * 1024 * 1024;
            } else if (size.endsWith("GB")) {
                return Long.parseLong(size.substring(0, size.length() - 2)) * 1024 * 1024 * 1024;
            } else {
                return Long.parseLong(size); // 假设是字节
            }
        }
        
        /**
         * 检查文件大小是否允许
         */
        public boolean isAllowedSize(long fileSize) {
            return fileSize <= getMaxSizeInBytes();
        }
    }
}
