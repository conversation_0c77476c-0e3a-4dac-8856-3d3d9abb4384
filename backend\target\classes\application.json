{"server": {"port": 8080, "host": "0.0.0.0"}, "database": {"host": "localhost", "port": 5432, "database": "superblog", "username": "superblog", "password": "superblog123", "maxPoolSize": 20, "connectTimeout": 5000, "idleTimeout": 300000}, "redis": {"host": "localhost", "port": 6379, "password": null, "database": 0, "maxPoolSize": 10, "maxWaitingHandlers": 100}, "jwt": {"secret": "your-jwt-secret-key-change-in-production", "expirationTime": 86400}, "ai": {"openai": {"apiKey": "${OPENAI_API_KEY}", "baseUrl": "https://api.openai.com/v1", "model": "dall-e-3", "timeout": 60000}, "stabilityai": {"apiKey": "${STABILITY_API_KEY}", "baseUrl": "https://api.stability.ai/v1", "timeout": 60000}, "qwen": {"apiKey": "sk-4606dfde828a4f9aa7a43f5d53dddb9e", "baseUrl": "https://dashscope.aliyuncs.com/api/v1", "model": "qwen-turbo", "timeout": 60000}}, "file": {"uploadPath": "./uploads", "maxFileSize": 10485760, "allowedTypes": ["image/jpeg", "image/png", "image/gif", "image/webp"]}, "cors": {"allowedOrigins": ["http://localhost:3000", "http://localhost:8080"], "allowedMethods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "allowedHeaders": ["Content-Type", "Authorization", "X-Requested-With"], "allowCredentials": true}, "logging": {"level": "INFO", "pattern": "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"}}