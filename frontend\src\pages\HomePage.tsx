/**
 * 首页组件
 * SuperBlog的主页面，展示平台概览和主要功能
 */

import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { 
  Sparkles, 
  MessageSquare, 
  FileText, 
  Code, 
  Video, 
  Wrench,
  ArrowRight,
  Github,
  ExternalLink
} from 'lucide-react';
import { Button } from '@/components/ui/Button';

const HomePage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Hero Section */}
      <section className="relative py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <h1 className="text-4xl sm:text-6xl font-bold text-gray-900 dark:text-white mb-6">
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              SuperBlog
            </span>
          </h1>
          <p className="text-xl sm:text-2xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto">
            现代化的个人技术展示平台
          </p>
          <p className="text-lg text-gray-500 dark:text-gray-400 mb-12 max-w-2xl mx-auto">
            集成AI图片生成、智能问答、博客写作、项目展示、视频管理和实用工具的一站式平台
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="group">
              开始探索
              <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
            </Button>
            <Button variant="outline" size="lg" className="group">
              <Github className="mr-2 w-5 h-5" />
              查看源码
              <ExternalLink className="ml-2 w-4 h-4 opacity-0 group-hover:opacity-100 transition-opacity" />
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              核心功能
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              强大的功能集合，满足你的各种需求
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* AI图片生成 */}
            <FeatureCard
              icon={<Sparkles className="w-8 h-8" />}
              title="AI图片生成"
              description="基于先进的AI技术，支持文生图和图生图，创造无限可能"
              link="/ai/image"
              gradient="from-pink-500 to-rose-500"
            />

            {/* 通义千问 */}
            <FeatureCard
              icon={<MessageSquare className="w-8 h-8" />}
              title="通义千问AI"
              description="智能问答、文章生成、提示词优化，AI助手让创作更轻松"
              link="/ai/chat"
              gradient="from-blue-500 to-cyan-500"
            />

            {/* 博客文章 */}
            <FeatureCard
              icon={<FileText className="w-8 h-8" />}
              title="博客文章"
              description="Markdown编辑器，支持代码高亮、数学公式，专业的写作体验"
              link="/articles"
              gradient="from-green-500 to-emerald-500"
            />

            {/* 项目展示 */}
            <FeatureCard
              icon={<Code className="w-8 h-8" />}
              title="项目展示"
              description="展示你的代码项目，支持GitHub同步，技术栈标签管理"
              link="/projects"
              gradient="from-purple-500 to-violet-500"
            />

            {/* 视频管理 */}
            <FeatureCard
              icon={<Video className="w-8 h-8" />}
              title="视频管理"
              description="视频上传、播放、管理，支持多种格式和外部平台集成"
              link="/videos"
              gradient="from-orange-500 to-red-500"
            />

            {/* 实用工具 */}
            <FeatureCard
              icon={<Wrench className="w-8 h-8" />}
              title="实用工具"
              description="JSON格式化、二维码生成、密码生成等开发者常用工具"
              link="/tools"
              gradient="from-indigo-500 to-blue-500"
            />
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <StatCard number="1000+" label="AI图片生成" />
            <StatCard number="500+" label="技术文章" />
            <StatCard number="50+" label="开源项目" />
            <StatCard number="100+" label="实用工具" />
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 dark:text-white mb-6">
            开始你的创作之旅
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-300 mb-8">
            立即体验SuperBlog的强大功能，让AI助力你的创作
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/ai/image">
              <Button size="lg" className="w-full sm:w-auto">
                开始AI创作
              </Button>
            </Link>
            <Link to="/articles">
              <Button variant="outline" size="lg" className="w-full sm:w-auto">
                浏览文章
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

// 功能卡片组件
interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  link: string;
  gradient: string;
}

const FeatureCard: React.FC<FeatureCardProps> = ({ 
  icon, 
  title, 
  description, 
  link, 
  gradient 
}) => {
  return (
    <Link to={link} className="group">
      <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 group-hover:-translate-y-1 border border-gray-100 dark:border-gray-700">
        <div className={`w-16 h-16 rounded-xl bg-gradient-to-r ${gradient} flex items-center justify-center text-white mb-4 group-hover:scale-110 transition-transform duration-300`}>
          {icon}
        </div>
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
          {title}
        </h3>
        <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
          {description}
        </p>
        <div className="mt-4 flex items-center text-blue-600 dark:text-blue-400 font-medium group-hover:translate-x-1 transition-transform">
          了解更多
          <ArrowRight className="ml-1 w-4 h-4" />
        </div>
      </div>
    </Link>
  );
};

// 统计卡片组件
interface StatCardProps {
  number: string;
  label: string;
}

const StatCard: React.FC<StatCardProps> = ({ number, label }) => {
  return (
    <div className="text-center">
      <div className="text-3xl sm:text-4xl font-bold text-blue-600 dark:text-blue-400 mb-2">
        {number}
      </div>
      <div className="text-gray-600 dark:text-gray-300 font-medium">
        {label}
      </div>
    </div>
  );
};

export default HomePage;
