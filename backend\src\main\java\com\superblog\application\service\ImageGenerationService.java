package com.superblog.application.service;

import com.google.inject.Inject;
import com.superblog.application.dto.ImageGenerationRequest;
import com.superblog.application.dto.ImageGenerationResponse;
import com.superblog.domain.model.ImageGeneration;
import com.superblog.domain.repository.ImageGenerationRepository;
import com.superblog.infrastructure.external.ai.OpenAiClient;
import com.superblog.infrastructure.external.ai.StabilityAiClient;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * AI图片生成服务
 * 负责处理图片生成相关的业务逻辑
 */
public class ImageGenerationService {

    private static final Logger logger = LoggerFactory.getLogger(ImageGenerationService.class);

    private final ImageGenerationRepository imageGenerationRepository;
    private final OpenAiClient openAiClient;
    private final StabilityAiClient stabilityAiClient;

    @Inject
    public ImageGenerationService(
            ImageGenerationRepository imageGenerationRepository,
            OpenAiClient openAiClient,
            StabilityAiClient stabilityAiClient) {
        this.imageGenerationRepository = imageGenerationRepository;
        this.openAiClient = openAiClient;
        this.stabilityAiClient = stabilityAiClient;
    }

    /**
     * 创建文生图任务
     */
    public Future<ImageGenerationResponse> createTextToImageGeneration(ImageGenerationRequest request) {
        Promise<ImageGenerationResponse> promise = Promise.promise();

        logger.info("创建文生图任务，用户ID: {}, 提示词: {}", request.getUserId(), request.getPrompt());

        // 创建图片生成记录
        ImageGeneration generation = new ImageGeneration(
            request.getUserId(),
            ImageGeneration.GenerationType.TEXT_TO_IMAGE,
            request.getPrompt()
        );
        
        // 设置生成参数
        generation.setNegativePrompt(request.getNegativePrompt());
        generation.setWidth(request.getWidth());
        generation.setHeight(request.getHeight());
        generation.setStyle(request.getStyle());
        generation.setModel(request.getModel());
        generation.setSeed(request.getSeed());
        generation.setSteps(request.getSteps());
        generation.setCfgScale(request.getCfgScale());

        // 保存到数据库
        imageGenerationRepository.save(generation)
            .compose(savedGeneration -> {
                // 异步执行图片生成
                executeImageGeneration(savedGeneration);
                
                // 立即返回响应
                ImageGenerationResponse response = mapToResponse(savedGeneration);
                return Future.succeededFuture(response);
            })
            .onSuccess(promise::complete)
            .onFailure(throwable -> {
                logger.error("创建文生图任务失败", throwable);
                promise.fail(throwable);
            });

        return promise.future();
    }

    /**
     * 创建图生图任务
     */
    public Future<ImageGenerationResponse> createImageToImageGeneration(ImageGenerationRequest request) {
        Promise<ImageGenerationResponse> promise = Promise.promise();

        logger.info("创建图生图任务，用户ID: {}, 原图URL: {}", request.getUserId(), request.getOriginalImageUrl());

        // 创建图片生成记录
        ImageGeneration generation = new ImageGeneration(
            request.getUserId(),
            ImageGeneration.GenerationType.IMAGE_TO_IMAGE,
            request.getPrompt()
        );
        
        // 设置生成参数
        generation.setOriginalImageUrl(request.getOriginalImageUrl());
        generation.setNegativePrompt(request.getNegativePrompt());
        generation.setWidth(request.getWidth());
        generation.setHeight(request.getHeight());
        generation.setStyle(request.getStyle());
        generation.setModel(request.getModel());
        generation.setSeed(request.getSeed());
        generation.setSteps(request.getSteps());
        generation.setCfgScale(request.getCfgScale());

        // 保存到数据库
        imageGenerationRepository.save(generation)
            .compose(savedGeneration -> {
                // 异步执行图片生成
                executeImageGeneration(savedGeneration);
                
                // 立即返回响应
                ImageGenerationResponse response = mapToResponse(savedGeneration);
                return Future.succeededFuture(response);
            })
            .onSuccess(promise::complete)
            .onFailure(throwable -> {
                logger.error("创建图生图任务失败", throwable);
                promise.fail(throwable);
            });

        return promise.future();
    }

    /**
     * 根据ID获取图片生成记录
     */
    public Future<Optional<ImageGenerationResponse>> getGenerationById(Long id) {
        return imageGenerationRepository.findById(id)
            .map(optionalGeneration -> 
                optionalGeneration.map(this::mapToResponse)
            );
    }

    /**
     * 获取用户的图片生成记录列表
     */
    public Future<List<ImageGenerationResponse>> getUserGenerations(Long userId, int page, int size) {
        return imageGenerationRepository.findByUserId(userId, page, size)
            .map(generations -> 
                generations.stream()
                    .map(this::mapToResponse)
                    .collect(Collectors.toList())
            );
    }

    /**
     * 获取公开的图片生成记录列表
     */
    public Future<List<ImageGenerationResponse>> getPublicGenerations(int page, int size) {
        return imageGenerationRepository.findPublicGenerations(page, size)
            .map(generations -> 
                generations.stream()
                    .map(this::mapToResponse)
                    .collect(Collectors.toList())
            );
    }

    /**
     * 获取热门图片生成记录
     */
    public Future<List<ImageGenerationResponse>> getPopularGenerations(int limit) {
        return imageGenerationRepository.findPopularGenerations(limit)
            .map(generations -> 
                generations.stream()
                    .map(this::mapToResponse)
                    .collect(Collectors.toList())
            );
    }

    /**
     * 获取最新图片生成记录
     */
    public Future<List<ImageGenerationResponse>> getLatestGenerations(int limit) {
        return imageGenerationRepository.findLatestGenerations(limit)
            .map(generations -> 
                generations.stream()
                    .map(this::mapToResponse)
                    .collect(Collectors.toList())
            );
    }

    /**
     * 搜索图片生成记录
     */
    public Future<List<ImageGenerationResponse>> searchGenerations(String keyword, int page, int size) {
        return imageGenerationRepository.searchByKeyword(keyword, page, size)
            .map(generations -> 
                generations.stream()
                    .map(this::mapToResponse)
                    .collect(Collectors.toList())
            );
    }

    /**
     * 增加查看次数
     */
    public Future<Void> incrementViewCount(Long id) {
        return imageGenerationRepository.incrementViewCount(id);
    }

    /**
     * 增加点赞次数
     */
    public Future<Void> incrementLikeCount(Long id) {
        return imageGenerationRepository.incrementLikeCount(id);
    }

    /**
     * 删除图片生成记录
     */
    public Future<Boolean> deleteGeneration(Long id, Long userId) {
        return imageGenerationRepository.findById(id)
            .compose(optionalGeneration -> {
                if (optionalGeneration.isEmpty()) {
                    return Future.succeededFuture(false);
                }
                
                ImageGeneration generation = optionalGeneration.get();
                if (!generation.getUserId().equals(userId)) {
                    return Future.failedFuture(new RuntimeException("无权限删除此记录"));
                }
                
                return imageGenerationRepository.deleteById(id);
            });
    }

    /**
     * 异步执行图片生成
     */
    private void executeImageGeneration(ImageGeneration generation) {
        logger.info("开始执行图片生成，ID: {}", generation.getId());
        
        // 标记为处理中
        generation.markAsProcessing();
        imageGenerationRepository.update(generation)
            .compose(updatedGeneration -> {
                // 根据生成类型选择不同的AI服务
                if (generation.getGenerationType() == ImageGeneration.GenerationType.TEXT_TO_IMAGE) {
                    return executeTextToImageGeneration(updatedGeneration);
                } else {
                    return executeImageToImageGeneration(updatedGeneration);
                }
            })
            .onSuccess(result -> {
                logger.info("图片生成完成，ID: {}", generation.getId());
            })
            .onFailure(throwable -> {
                logger.error("图片生成失败，ID: {}", generation.getId(), throwable);
                
                // 标记为失败
                generation.markAsFailed(throwable.getMessage());
                imageGenerationRepository.update(generation);
            });
    }

    /**
     * 执行文生图
     */
    private Future<ImageGeneration> executeTextToImageGeneration(ImageGeneration generation) {
        long startTime = System.currentTimeMillis();
        
        // 根据模型选择AI服务
        if ("dall-e-3".equals(generation.getModel())) {
            return openAiClient.generateImage(generation)
                .compose(imageUrl -> {
                    int generationTime = (int) ((System.currentTimeMillis() - startTime) / 1000);
                    generation.markAsCompleted(imageUrl, imageUrl, generationTime);
                    return imageGenerationRepository.update(generation);
                });
        } else {
            return stabilityAiClient.generateImage(generation)
                .compose(imageUrl -> {
                    int generationTime = (int) ((System.currentTimeMillis() - startTime) / 1000);
                    generation.markAsCompleted(imageUrl, imageUrl, generationTime);
                    return imageGenerationRepository.update(generation);
                });
        }
    }

    /**
     * 执行图生图
     */
    private Future<ImageGeneration> executeImageToImageGeneration(ImageGeneration generation) {
        long startTime = System.currentTimeMillis();
        
        return stabilityAiClient.generateImageFromImage(generation)
            .compose(imageUrl -> {
                int generationTime = (int) ((System.currentTimeMillis() - startTime) / 1000);
                generation.markAsCompleted(imageUrl, imageUrl, generationTime);
                return imageGenerationRepository.update(generation);
            });
    }

    /**
     * 将领域模型映射为响应DTO
     */
    private ImageGenerationResponse mapToResponse(ImageGeneration generation) {
        ImageGenerationResponse response = new ImageGenerationResponse();
        response.setId(generation.getId());
        response.setUserId(generation.getUserId());
        response.setGenerationType(generation.getGenerationType().name());
        response.setPrompt(generation.getPrompt());
        response.setNegativePrompt(generation.getNegativePrompt());
        response.setOriginalImageUrl(generation.getOriginalImageUrl());
        response.setGeneratedImageUrl(generation.getGeneratedImageUrl());
        response.setThumbnailUrl(generation.getThumbnailUrl());
        response.setWidth(generation.getWidth());
        response.setHeight(generation.getHeight());
        response.setStyle(generation.getStyle());
        response.setModel(generation.getModel());
        response.setSeed(generation.getSeed());
        response.setSteps(generation.getSteps());
        response.setCfgScale(generation.getCfgScale());
        response.setStatus(generation.getStatus().name());
        response.setErrorMessage(generation.getErrorMessage());
        response.setGenerationTime(generation.getGenerationTime());
        response.setIsPublic(generation.getIsPublic());
        response.setViewCount(generation.getViewCount());
        response.setLikeCount(generation.getLikeCount());
        response.setCreatedAt(generation.getCreatedAt());
        response.setUpdatedAt(generation.getUpdatedAt());
        return response;
    }
}
