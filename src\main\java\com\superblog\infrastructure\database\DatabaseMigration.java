package com.superblog.infrastructure.database;

import com.google.inject.Inject;
import com.superblog.config.DatabaseConfig;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.Vertx;
import org.flywaydb.core.Flyway;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 数据库迁移服务
 * 负责执行数据库结构迁移
 */
public class DatabaseMigration {

    private static final Logger logger = LoggerFactory.getLogger(DatabaseMigration.class);

    private final Vertx vertx;
    private final DatabaseConfig databaseConfig;

    @Inject
    public DatabaseMigration(Vertx vertx, DatabaseConfig databaseConfig) {
        this.vertx = vertx;
        this.databaseConfig = databaseConfig;
    }

    /**
     * 执行数据库迁移
     */
    public Future<Void> migrate() {
        Promise<Void> promise = Promise.promise();

        vertx.executeBlocking(blockingPromise -> {
            try {
                logger.info("开始执行数据库迁移...");

                Flyway flyway = Flyway.configure()
                    .dataSource(
                        databaseConfig.getJdbcUrl(),
                        databaseConfig.username(),
                        databaseConfig.password()
                    )
                    .locations("classpath:db/migration")
                    .baselineOnMigrate(true)
                    .validateOnMigrate(true)
                    .load();

                // 执行迁移
                int migrationsExecuted = flyway.migrate().migrationsExecuted;
                
                if (migrationsExecuted > 0) {
                    logger.info("数据库迁移完成，执行了 {} 个迁移脚本", migrationsExecuted);
                } else {
                    logger.info("数据库已是最新版本，无需迁移");
                }

                blockingPromise.complete();
            } catch (Exception e) {
                logger.error("数据库迁移失败", e);
                blockingPromise.fail(e);
            }
        }, false)
        .onSuccess(v -> promise.complete())
        .onFailure(promise::fail);

        return promise.future();
    }

    /**
     * 获取数据库迁移信息
     */
    public Future<String> getInfo() {
        Promise<String> promise = Promise.promise();

        vertx.executeBlocking(blockingPromise -> {
            try {
                Flyway flyway = Flyway.configure()
                    .dataSource(
                        databaseConfig.getJdbcUrl(),
                        databaseConfig.username(),
                        databaseConfig.password()
                    )
                    .locations("classpath:db/migration")
                    .load();

                var info = flyway.info();
                StringBuilder sb = new StringBuilder();
                sb.append("数据库迁移状态:\n");
                
                for (var migration : info.all()) {
                    sb.append(String.format("版本: %s, 描述: %s, 状态: %s\n",
                        migration.getVersion(),
                        migration.getDescription(),
                        migration.getState()
                    ));
                }

                blockingPromise.complete(sb.toString());
            } catch (Exception e) {
                logger.error("获取数据库迁移信息失败", e);
                blockingPromise.fail(e);
            }
        }, false)
        .onSuccess(promise::complete)
        .onFailure(promise::fail);

        return promise.future();
    }
}
