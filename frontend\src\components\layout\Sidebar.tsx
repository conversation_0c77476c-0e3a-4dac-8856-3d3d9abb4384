/**
 * 侧边栏导航组件
 * 包含主要的导航菜单
 */

import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { 
  Home,
  Sparkles,
  MessageSquare,
  FileText,
  Code,
  Video,
  Wrench,
  Info,
  ChevronRight
} from 'lucide-react';
import { useAppStore } from '@/stores/appStore';
import { cn } from '@/utils';

// 导航菜单配置
const navigationItems = [
  {
    id: 'home',
    label: '首页',
    icon: Home,
    path: '/',
  },
  {
    id: 'ai',
    label: 'AI功能',
    icon: Sparkles,
    children: [
      {
        id: 'ai-image',
        label: 'AI图片生成',
        icon: Sparkles,
        path: '/ai/image',
      },
      {
        id: 'ai-chat',
        label: '通义千问',
        icon: MessageSquare,
        path: '/ai/chat',
      },
    ],
  },
  {
    id: 'articles',
    label: '技术文章',
    icon: FileText,
    path: '/articles',
  },
  {
    id: 'projects',
    label: '项目展示',
    icon: Code,
    path: '/projects',
  },
  {
    id: 'videos',
    label: '视频管理',
    icon: Video,
    path: '/videos',
  },
  {
    id: 'tools',
    label: '实用工具',
    icon: Wrench,
    path: '/tools',
  },
  {
    id: 'about',
    label: '关于',
    icon: Info,
    path: '/about',
  },
];

const Sidebar: React.FC = () => {
  const { sidebarOpen, mobileMenuOpen, toggleMobileMenu } = useAppStore();
  const location = useLocation();
  const [expandedItems, setExpandedItems] = React.useState<string[]>(['ai']);

  const toggleExpanded = (itemId: string) => {
    setExpandedItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  const isParentActive = (children: any[]) => {
    return children.some(child => isActive(child.path));
  };

  return (
    <>
      {/* Desktop Sidebar */}
      <aside className={cn(
        'fixed left-0 top-16 h-[calc(100vh-4rem)] bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 transition-all duration-300 ease-in-out z-30',
        'hidden lg:block',
        sidebarOpen ? 'w-64' : 'w-16'
      )}>
        <nav className="h-full overflow-y-auto py-4">
          <ul className="space-y-1 px-2">
            {navigationItems.map((item) => (
              <NavigationItem
                key={item.id}
                item={item}
                isCollapsed={!sidebarOpen}
                isActive={item.path ? isActive(item.path) : isParentActive(item.children || [])}
                isExpanded={expandedItems.includes(item.id)}
                onToggleExpanded={() => toggleExpanded(item.id)}
              />
            ))}
          </ul>
        </nav>
      </aside>

      {/* Mobile Sidebar */}
      <aside className={cn(
        'fixed left-0 top-16 h-[calc(100vh-4rem)] w-64 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 transition-transform duration-300 ease-in-out z-50',
        'lg:hidden',
        mobileMenuOpen ? 'translate-x-0' : '-translate-x-full'
      )}>
        <nav className="h-full overflow-y-auto py-4">
          <ul className="space-y-1 px-2">
            {navigationItems.map((item) => (
              <NavigationItem
                key={item.id}
                item={item}
                isCollapsed={false}
                isActive={item.path ? isActive(item.path) : isParentActive(item.children || [])}
                isExpanded={expandedItems.includes(item.id)}
                onToggleExpanded={() => toggleExpanded(item.id)}
                onItemClick={toggleMobileMenu}
              />
            ))}
          </ul>
        </nav>
      </aside>
    </>
  );
};

// 导航项组件
interface NavigationItemProps {
  item: any;
  isCollapsed: boolean;
  isActive: boolean;
  isExpanded: boolean;
  onToggleExpanded: () => void;
  onItemClick?: () => void;
}

const NavigationItem: React.FC<NavigationItemProps> = ({
  item,
  isCollapsed,
  isActive,
  isExpanded,
  onToggleExpanded,
  onItemClick,
}) => {
  const Icon = item.icon;
  const hasChildren = item.children && item.children.length > 0;

  if (hasChildren) {
    return (
      <li>
        <button
          onClick={onToggleExpanded}
          className={cn(
            'w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors',
            'hover:bg-gray-100 dark:hover:bg-gray-700',
            isActive 
              ? 'bg-blue-50 text-blue-700 dark:bg-blue-900/50 dark:text-blue-300' 
              : 'text-gray-700 dark:text-gray-300'
          )}
        >
          <Icon className="w-5 h-5 flex-shrink-0" />
          {!isCollapsed && (
            <>
              <span className="ml-3 flex-1 text-left">{item.label}</span>
              <ChevronRight 
                className={cn(
                  'w-4 h-4 transition-transform',
                  isExpanded && 'rotate-90'
                )}
              />
            </>
          )}
        </button>
        
        {!isCollapsed && isExpanded && (
          <ul className="mt-1 space-y-1 ml-4">
            {item.children.map((child: any) => (
              <li key={child.id}>
                <Link
                  to={child.path}
                  onClick={onItemClick}
                  className={cn(
                    'flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors',
                    'hover:bg-gray-100 dark:hover:bg-gray-700',
                    isActive && location.pathname === child.path
                      ? 'bg-blue-50 text-blue-700 dark:bg-blue-900/50 dark:text-blue-300'
                      : 'text-gray-600 dark:text-gray-400'
                  )}
                >
                  <child.icon className="w-4 h-4 flex-shrink-0" />
                  <span className="ml-3">{child.label}</span>
                </Link>
              </li>
            ))}
          </ul>
        )}
      </li>
    );
  }

  return (
    <li>
      <Link
        to={item.path}
        onClick={onItemClick}
        className={cn(
          'flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors',
          'hover:bg-gray-100 dark:hover:bg-gray-700',
          isActive 
            ? 'bg-blue-50 text-blue-700 dark:bg-blue-900/50 dark:text-blue-300' 
            : 'text-gray-700 dark:text-gray-300'
        )}
        title={isCollapsed ? item.label : undefined}
      >
        <Icon className="w-5 h-5 flex-shrink-0" />
        {!isCollapsed && (
          <span className="ml-3">{item.label}</span>
        )}
      </Link>
    </li>
  );
};

export default Sidebar;
