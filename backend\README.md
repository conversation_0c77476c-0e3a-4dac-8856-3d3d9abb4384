# SuperBlog Backend

SuperBlog 个人技术展示平台后端服务，基于 Java 21 + Vert.x 4.x 构建。

## 🚀 技术栈

- **Java 21** - 最新的LTS版本，支持现代Java特性
- **Vert.x 4.x** - 高性能异步Web框架
- **PostgreSQL** - 主数据库
- **Redis** - 缓存和会话存储
- **Flyway** - 数据库迁移管理
- **Google Guice** - 依赖注入框架
- **Logback** - 日志框架
- **Maven** - 构建工具

## 📁 项目结构

```
backend/
├── src/main/java/com/superblog/
│   ├── MainVerticle.java                    # 主启动类
│   ├── config/                              # 配置相关
│   │   ├── AppConfig.java                   # 应用配置
│   │   ├── ConfigModule.java                # 配置模块
│   │   ├── DatabaseConfig.java              # 数据库配置
│   │   └── ...
│   ├── domain/                              # 领域层
│   │   ├── model/                           # 领域模型
│   │   └── repository/                      # 仓储接口
│   ├── application/                         # 应用层
│   │   ├── service/                         # 应用服务
│   │   └── dto/                             # 数据传输对象
│   └── infrastructure/                      # 基础设施层
│       ├── database/                        # 数据库相关
│       ├── repository/                      # 仓储实现
│       ├── external/                        # 外部服务
│       └── web/                             # Web相关
├── src/main/resources/
│   ├── application.json                     # 主配置文件
│   ├── application-dev.json                 # 开发环境配置
│   ├── logback.xml                          # 日志配置
│   └── db/migration/                        # 数据库迁移脚本
├── api-tests.sh                             # API测试脚本
├── pom.xml                                  # Maven配置
└── README.md                                # 项目说明
```

## 🎯 核心功能

### 1. AI图片生成工具 (🔥 核心功能)
- **文生图**: 根据文字描述生成图片
- **图生图**: 基于原图进行风格转换
- **支持多种AI模型**: OpenAI DALL-E, Stability AI
- **生成历史管理**: 保存、查看、分享生成记录
- **参数配置**: 尺寸、风格、步数、CFG等

### 2. 视频展示模块
- 视频上传和管理
- 支持外部视频链接 (YouTube, Bilibili)
- 视频分类和标签
- 播放统计和评论

### 3. 博客模块
- Markdown文章编辑
- 分类和标签管理
- 全文搜索
- 阅读统计和评论

### 4. 项目展示模块
- GitHub项目同步
- 技术栈展示
- 项目演示链接
- Star和Fork统计

### 5. 小工具模块
- JSON格式化
- 二维码生成
- 密码生成器
- 时间戳转换
- Base64编解码

## 🛠️ 开发环境搭建

### 前置要求

- Java 21+
- Maven 3.8+
- PostgreSQL 12+
- Redis 6+
- Docker (可选)

### 1. 克隆项目

```bash
git clone <repository-url>
cd SuperBlog/backend
```

### 2. 配置数据库

```bash
# 创建PostgreSQL数据库
createdb superblog_dev
createuser superblog
```

### 3. 配置环境变量

```bash
# 复制配置文件
cp src/main/resources/application.json src/main/resources/application-dev.json

# 设置环境变量
export OPENAI_API_KEY="your-openai-api-key"
export STABILITY_API_KEY="your-stability-api-key"
```

### 4. 运行数据库迁移

```bash
mvn flyway:migrate
```

### 5. 启动应用

```bash
# 开发模式
mvn compile exec:java -Dexec.mainClass="io.vertx.core.Launcher" -Dexec.args="run com.superblog.MainVerticle"

# 或者使用Vert.x插件
mvn vertx:run
```

### 6. 验证启动

```bash
curl http://localhost:8080/api/health
```

## 🧪 API测试

项目提供了完整的API测试脚本：

```bash
# 给脚本执行权限
chmod +x api-tests.sh

# 运行所有API测试
./api-tests.sh
```

### 主要API接口

#### AI图片生成
```bash
# 创建文生图任务
POST /api/ai/text-to-image
{
  "user_id": 1,
  "prompt": "一只可爱的橘猫坐在樱花树下，动漫风格，高清画质",
  "width": 512,
  "height": 512,
  "style": "动漫风格"
}

# 获取公开图片生成记录
GET /api/ai/generations/public?page=0&size=10

# 搜索图片生成记录
GET /api/ai/generations/search?keyword=猫&page=0&size=10
```

#### 其他模块
```bash
# 获取视频列表
GET /api/videos

# 获取文章列表
GET /api/articles

# 获取项目列表
GET /api/projects
```

## 🐳 Docker部署

### 使用Docker Compose

```bash
# 进入部署目录
cd ../dev-ops

# 启动所有服务
docker-compose up -d

# 查看日志
docker-compose logs -f superblog-backend
```

### 单独构建镜像

```bash
# 构建应用
mvn clean package

# 构建Docker镜像
docker build -f ../dev-ops/Dockerfile -t superblog-backend .

# 运行容器
docker run -p 8080:8080 superblog-backend
```

## 📊 监控和日志

### 日志文件位置
- 应用日志: `logs/superblog.log`
- 错误日志: `logs/superblog-error.log`

### 健康检查
```bash
curl http://localhost:8080/api/health
```

### 性能监控
应用集成了Micrometer指标收集，可以与Prometheus等监控系统集成。

## 🔧 配置说明

### 主要配置项

```json
{
  "server": {
    "port": 8080,
    "host": "0.0.0.0"
  },
  "database": {
    "host": "localhost",
    "port": 5432,
    "database": "superblog",
    "username": "superblog",
    "password": "superblog123"
  },
  "ai": {
    "openai": {
      "apiKey": "${OPENAI_API_KEY}",
      "model": "dall-e-3"
    },
    "stabilityai": {
      "apiKey": "${STABILITY_API_KEY}"
    }
  }
}
```

### 环境特定配置
- `application.json` - 默认配置
- `application-dev.json` - 开发环境
- `application-test.json` - 测试环境
- `application-prod.json` - 生产环境

## 🚀 部署指南

### 生产环境部署

1. **构建应用**
```bash
mvn clean package
```

2. **配置环境变量**
```bash
export PROFILE=prod
export DB_HOST=your-db-host
export REDIS_HOST=your-redis-host
export OPENAI_API_KEY=your-api-key
```

3. **运行应用**
```bash
java -jar target/superblog-backend-1.0.0-SNAPSHOT-fat.jar
```

### 使用Docker部署
参考 `dev-ops/` 目录下的Docker配置文件。

## 🤝 开发指南

### 代码规范
- 所有类、方法、字段使用中文注释
- 遵循Java编码规范
- 使用DDD架构模式
- 合理的异常处理和日志记录

### 添加新功能
1. 在 `domain/model` 中定义领域模型
2. 在 `domain/repository` 中定义仓储接口
3. 在 `infrastructure/repository` 中实现仓储
4. 在 `application/service` 中实现业务逻辑
5. 在 `infrastructure/web/controller` 中实现API接口
6. 在 `api-tests.sh` 中添加测试用例

## 📝 TODO

- [ ] 完善用户认证和授权
- [ ] 实现文件上传服务
- [ ] 添加API限流功能
- [ ] 完善监控和告警
- [ ] 添加单元测试和集成测试
- [ ] 实现缓存策略
- [ ] 优化数据库查询性能

## 📄 许可证

MIT License
