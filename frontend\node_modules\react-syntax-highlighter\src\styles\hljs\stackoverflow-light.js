export default {
    "hljs": {
        "display": "block",
        "overflowX": "auto",
        "padding": "0.5em",
        "color": "#2f3337",
        "background": "#f6f6f6"
    },
    "hljs-comment": {
        "color": "#656e77"
    },
    "hljs-keyword": {
        "color": "#015692"
    },
    "hljs-selector-tag": {
        "color": "#015692"
    },
    "hljs-meta-keyword": {
        "color": "#015692"
    },
    "hljs-doctag": {
        "color": "#015692"
    },
    "hljs-section": {
        "color": "#015692"
    },
    "hljs-selector-class": {
        "color": "#015692"
    },
    "hljs-meta": {
        "color": "#015692"
    },
    "hljs-selector-pseudo": {
        "color": "#015692"
    },
    "hljs-attr": {
        "color": "#015692"
    },
    "hljs-attribute": {
        "color": "#803378"
    },
    "hljs-name": {
        "color": "#b75501"
    },
    "hljs-type": {
        "color": "#b75501"
    },
    "hljs-number": {
        "color": "#b75501"
    },
    "hljs-selector-id": {
        "color": "#b75501"
    },
    "hljs-quote": {
        "color": "#b75501"
    },
    "hljs-template-tag": {
        "color": "#b75501"
    },
    "hljs-built_in": {
        "color": "#b75501"
    },
    "hljs-title": {
        "color": "#b75501"
    },
    "hljs-literal": {
        "color": "#b75501"
    },
    "hljs-string": {
        "color": "#54790d"
    },
    "hljs-regexp": {
        "color": "#54790d"
    },
    "hljs-symbol": {
        "color": "#54790d"
    },
    "hljs-variable": {
        "color": "#54790d"
    },
    "hljs-template-variable": {
        "color": "#54790d"
    },
    "hljs-link": {
        "color": "#54790d"
    },
    "hljs-selector-attr": {
        "color": "#54790d"
    },
    "hljs-meta-string": {
        "color": "#54790d"
    },
    "hljs-bullet": {
        "color": "#535a60"
    },
    "hljs-code": {
        "color": "#535a60"
    },
    "hljs-deletion": {
        "color": "#c02d2e"
    },
    "hljs-addition": {
        "color": "#2f6f44"
    },
    "hljs-emphasis": {
        "fontStyle": "italic"
    },
    "hljs-strong": {
        "fontWeight": "bold"
    }
}