package com.superblog.infrastructure.web.response;

import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;

/**
 * API响应工具类
 * 提供统一的响应格式
 */
public class ApiResponse {

    /**
     * 成功响应
     */
    public static void success(RoutingContext context, Object data) {
        success(context, data, "操作成功");
    }

    /**
     * 成功响应（带消息）
     */
    public static void success(RoutingContext context, Object data, String message) {
        JsonObject response = new JsonObject()
            .put("success", true)
            .put("code", 200)
            .put("message", message)
            .put("data", data)
            .put("timestamp", System.currentTimeMillis());

        context.response()
            .setStatusCode(200)
            .putHeader("Content-Type", "application/json; charset=utf-8")
            .end(response.encode());
    }

    /**
     * 错误请求响应 (400)
     */
    public static void badRequest(RoutingContext context, String message) {
        error(context, 400, message);
    }

    /**
     * 未授权响应 (401)
     */
    public static void unauthorized(RoutingContext context, String message) {
        error(context, 401, message);
    }

    /**
     * 禁止访问响应 (403)
     */
    public static void forbidden(RoutingContext context, String message) {
        error(context, 403, message);
    }

    /**
     * 资源不存在响应 (404)
     */
    public static void notFound(RoutingContext context, String message) {
        error(context, 404, message);
    }

    /**
     * 服务器内部错误响应 (500)
     */
    public static void internalError(RoutingContext context, String message) {
        error(context, 500, message);
    }

    /**
     * 通用错误响应
     */
    public static void error(RoutingContext context, int statusCode, String message) {
        JsonObject response = new JsonObject()
            .put("success", false)
            .put("code", statusCode)
            .put("message", message)
            .put("data", null)
            .put("timestamp", System.currentTimeMillis());

        context.response()
            .setStatusCode(statusCode)
            .putHeader("Content-Type", "application/json; charset=utf-8")
            .end(response.encode());
    }

    /**
     * 分页响应
     */
    public static void page(RoutingContext context, Object data, long total, int page, int size) {
        JsonObject pageInfo = new JsonObject()
            .put("page", page)
            .put("size", size)
            .put("total", total)
            .put("pages", (total + size - 1) / size);

        JsonObject response = new JsonObject()
            .put("success", true)
            .put("code", 200)
            .put("message", "操作成功")
            .put("data", data)
            .put("page", pageInfo)
            .put("timestamp", System.currentTimeMillis());

        context.response()
            .setStatusCode(200)
            .putHeader("Content-Type", "application/json; charset=utf-8")
            .end(response.encode());
    }
}
