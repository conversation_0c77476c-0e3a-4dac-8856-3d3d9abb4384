package com.superblog.config;

/**
 * CORS配置类
 * 支持从YAML配置文件中读取配置
 */
public record CorsConfig(
    String allowedOrigins,
    String allowedMethods,
    String allowedHeaders,
    Long maxAge
) {
    public CorsConfig {
        // 设置默认值
        if (allowedOrigins == null || allowedOrigins.isEmpty()) {
            allowedOrigins = "*";
        }
        if (allowedMethods == null || allowedMethods.isEmpty()) {
            allowedMethods = "GET,POST,PUT,DELETE,OPTIONS";
        }
        if (allowedHeaders == null || allowedHeaders.isEmpty()) {
            allowedHeaders = "*";
        }
        if (maxAge == null) {
            maxAge = 3600L;
        }
    }
    
    /**
     * 获取允许的源列表
     */
    public String[] getAllowedOriginsArray() {
        return allowedOrigins.split(",");
    }
    
    /**
     * 获取允许的方法列表
     */
    public String[] getAllowedMethodsArray() {
        return allowedMethods.split(",");
    }
    
    /**
     * 获取允许的头部列表
     */
    public String[] getAllowedHeadersArray() {
        return allowedHeaders.split(",");
    }
}
