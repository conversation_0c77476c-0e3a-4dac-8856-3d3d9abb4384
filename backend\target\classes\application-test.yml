server:
  port: 8080
  servlet:
    context-path: /api

# 线程池配置
thread:
  pool:
    executor:
      config:
        core-pool-size: 5
        max-pool-size: 10
        keep-alive-time: 5000
        block-queue-size: 1000
        policy: CallerRunsPolicy

# 数据库配置 - 测试环境使用内存数据库
spring:
  datasource:
    username: sa
    password: 
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    hikari:
      maximum-pool-size: 5
      minimum-idle: 1
      idle-timeout: 30000
      connection-timeout: 30000
      max-lifetime: 1800000
  
  # H2数据库控制台
  h2:
    console:
      enabled: true
      path: /h2-console
  
  # Redis配置 - 测试环境使用嵌入式Redis
  redis:
    host: localhost
    port: 6379
    password: 
    timeout: 5000
  
  main:
    allow-bean-definition-overriding: true

# AI服务配置 - 测试环境使用mock数据
ai:
  # OpenAI配置
  openai:
    base-url: http://localhost:8080/mock/openai
    api-key: test-key
    model: gpt-3.5-turbo
    timeout: 5000
    
  # 通义千问配置  
  qwen:
    base-url: http://localhost:8080/mock/qwen
    api-key: test-key
    model: qwen-turbo
    timeout: 5000
    
  # Stability AI配置
  stability:
    base-url: http://localhost:8080/mock/stability
    api-key: test-key
    timeout: 5000

# 文件存储配置
file:
  upload:
    path: ./test-uploads
    max-size: 1MB
    allowed-types: jpg,jpeg,png,gif
  
# JWT配置
jwt:
  secret: superblog-test-secret-key
  expiration: 3600000 # 1小时

# CORS配置
cors:
  allowed-origins: "*"
  allowed-methods: GET,POST,PUT,DELETE,OPTIONS
  allowed-headers: "*"
  max-age: 3600

# 日志配置
logging:
  level:
    root: warn
    com.superblog: debug
  config: classpath:logback.xml
