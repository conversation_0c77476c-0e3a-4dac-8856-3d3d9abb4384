<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="d0cd7e72-982a-49e3-a2ea-4302093eb833" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/superblog/Application.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/superblog/Application.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/superblog/MainVerticle.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/superblog/MainVerticle.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/superblog/application/service/ImageGenerationService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/superblog/application/service/ImageGenerationService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/superblog/config/AiConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/superblog/config/AiConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/superblog/config/AppConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/superblog/config/AppConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/superblog/config/ConfigModule.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/superblog/config/ConfigModule.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/superblog/config/CorsConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/superblog/config/CorsConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/superblog/config/FileConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/superblog/config/FileConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/superblog/config/JwtConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/superblog/config/JwtConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/superblog/config/ServiceModule.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/superblog/config/ServiceModule.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/superblog/infrastructure/external/ai/OpenAiClient.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/superblog/infrastructure/external/ai/OpenAiClient.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/superblog/infrastructure/external/ai/QwenClient.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/superblog/infrastructure/external/ai/QwenClient.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/superblog/infrastructure/web/SimpleRouterFactory.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/superblog/infrastructure/web/SimpleRouterFactory.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/superblog/infrastructure/web/controller/ImageGenerationController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/superblog/infrastructure/web/controller/ImageGenerationController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/application-dev.json" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application-dev.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/db/migration/V1__Create_base_tables.sql" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/db/migration/V1__Create_base_tables.sql" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="KubernetesApiPersistence"><![CDATA[{}]]></component>
  <component name="KubernetesApiProvider"><![CDATA[{
  "isMigrated": true
}]]></component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 5
}]]></component>
  <component name="ProjectId" id="2ywm4X2oHoLHVOYFEVHyxpEZQMv" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Application.Application.executor": "Debug",
    "Application.MinimalApplication.executor": "Run",
    "Application.SimpleApplication.executor": "Run",
    "Docker.dev-ops: Compose Deployment.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "git-widget-placeholder": "master",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "D:/Project/github/spring-ai-alibaba-examples/pom.xml",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager" selected="Docker.dev-ops: Compose Deployment">
    <configuration name="Application" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.superblog.Application" />
      <module name="superblog-backend" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.superblog.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="MinimalApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.superblog.MinimalApplication" />
      <module name="superblog-backend" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.superblog.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SimpleApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.superblog.SimpleApplication" />
      <module name="superblog-backend" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.superblog.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="JetRunConfigurationType">
      <module name="backend" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="backend" />
      <option name="filePath" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="docker-deploy" factoryName="docker-compose.yml" temporary="true">
      <deployment type="docker-compose.yml" />
      <method v="2" />
    </configuration>
    <configuration name="dev-ops: Compose Deployment" type="docker-deploy" factoryName="docker-compose.yml" temporary="true" server-name="Docker">
      <deployment type="docker-compose.yml">
        <settings>
          <option name="sourceFilePath" value="dev-ops/docker-compose.yml" />
        </settings>
      </deployment>
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Application.Application" />
        <item itemvalue="Docker.dev-ops: Compose Deployment" />
        <item itemvalue="Application.MinimalApplication" />
        <item itemvalue="Application.SimpleApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-a94e463ab2e7-intellij.indexing.shared.core-IU-243.26053.27" />
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-IU-243.26053.27" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="d0cd7e72-982a-49e3-a2ea-4302093eb833" name="Changes" comment="" />
      <created>1750753818713</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750753818713</updated>
      <workItem from="1750753819752" duration="13341000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/superblog/infrastructure/web/SimpleRouterFactory.java</url>
          <line>116</line>
          <option name="timeStamp" value="2" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/superblog/infrastructure/web/SimpleRouterFactory.java</url>
          <line>121</line>
          <option name="timeStamp" value="3" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>