server:
  port: 8080
  servlet:
    context-path: /api

# 线程池配置
thread:
  pool:
    executor:
      config:
        core-pool-size: 5
        max-pool-size: 10
        keep-alive-time: 5000
        block-queue-size: 1000
        policy: CallerRunsPolicy

# 数据库配置
spring:
  datasource:
    username: superblog
    password: superblog123
    url: **************************************************************************************************************
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 5
      minimum-idle: 2
      idle-timeout: 30000
      connection-timeout: 30000
      max-lifetime: 1800000
  
  # Redis配置
  redis:
    host: localhost
    port: 6379
    password: 
    timeout: 5000
    lettuce:
      pool:
        max-active: 5
        max-idle: 3
        min-idle: 1
        max-wait: -1
  
  main:
    allow-bean-definition-overriding: true

# AI服务配置 - 本地开发使用mock数据，不需要真实API Key
ai:
  # OpenAI配置
  openai:
    base-url: https://api.openai.com/v1
    api-key: 
    model: gpt-3.5-turbo
    timeout: 30000
    
  # 通义千问配置  
  qwen:
    base-url: https://dashscope.aliyuncs.com/api/v1
    api-key: 
    model: qwen-turbo
    timeout: 30000
    
  # Stability AI配置
  stability:
    base-url: https://api.stability.ai/v1
    api-key: 
    timeout: 60000

# 文件存储配置
file:
  upload:
    path: ./uploads
    max-size: 5MB
    allowed-types: jpg,jpeg,png,gif,webp
  
# JWT配置
jwt:
  secret: superblog-local-secret-key-for-development-only
  expiration: 86400000 # 24小时

# CORS配置
cors:
  allowed-origins: http://localhost:3000,http://127.0.0.1:3000,http://localhost:5173
  allowed-methods: GET,POST,PUT,DELETE,OPTIONS
  allowed-headers: "*"
  max-age: 3600

# 日志配置
logging:
  level:
    root: debug
    com.superblog: debug
    org.springframework: info
  config: classpath:logback.xml
