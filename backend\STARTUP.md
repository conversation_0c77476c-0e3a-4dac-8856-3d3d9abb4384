# SuperBlog 启动指南

## 🚀 多种启动方式

现在提供4种启动方式，按推荐顺序：

### 方法1：MinimalApplication（推荐）

**在IDE中运行：**
1. 右键点击 `MinimalApplication.java`
2. 选择 `Run 'MinimalApplication.main()'`

**特点：**
- ✅ 无外部依赖，确保能启动
- ✅ 包含所有核心API接口
- ✅ 完整的错误处理
- ✅ 通义千问API集成

### 方法2：Application（完整版）

**在IDE中运行：**
1. 右键点击 `Application.java`
2. 选择 `Run 'Application.main()'`

**特点：**
- ✅ 完整的配置文件支持
- ✅ 专业的日志系统
- ✅ 环境变量处理
- ✅ 启动信息详细

### 方法3：SimpleApplication

**在IDE中运行：**
1. 右键点击 `SimpleApplication.java`
2. 选择 `Run 'SimpleApplication.main()'`

### 方法4：SimpleMainVerticle

**在IDE中运行：**
1. 右键点击 `SimpleMainVerticle.java`
2. 选择 `Run 'SimpleMainVerticle.main()'`

### 方法5：StartupSelector（一键选择）

**在IDE中运行：**
1. 右键点击 `StartupSelector.java`
2. 选择 `Run 'StartupSelector.main()'`
3. 会自动启动MinimalApplication

## 🧪 测试API

启动成功后，运行测试脚本：

```bash
# 给脚本执行权限
chmod +x minimal-test.sh

# 运行测试
./minimal-test.sh
```

## 📋 可用的API接口

### 基础功能
- `GET /api/health` - 健康检查

### AI图片生成
- `POST /api/ai/text-to-image` - 创建文生图任务
- `GET /api/ai/generations/public` - 获取公开生成记录

### 通义千问AI
- `POST /api/ai/chat` - 智能问答
- `POST /api/ai/generate-article` - 生成博客文章
- `POST /api/ai/optimize-prompt` - 优化绘画提示词
- `GET /api/ai/qwen/test` - API连接测试

### 其他模块
- `GET /api/videos` - 视频列表
- `GET /api/articles` - 文章列表
- `GET /api/projects` - 项目列表

## 🔧 API使用示例

### 1. 健康检查
```bash
curl http://localhost:8080/api/health
```

### 2. 通义千问问答
```bash
curl -X POST http://localhost:8080/api/ai/chat \
  -H "Content-Type: application/json" \
  -d '{"question": "什么是Java？"}'
```

### 3. 生成博客文章
```bash
curl -X POST http://localhost:8080/api/ai/generate-article \
  -H "Content-Type: application/json" \
  -d '{"topic": "Spring Boot入门教程"}'
```

### 4. AI文生图
```bash
curl -X POST http://localhost:8080/api/ai/text-to-image \
  -H "Content-Type: application/json" \
  -d '{"prompt": "一只可爱的橘猫"}'
```

### 5. 优化绘画提示词
```bash
curl -X POST http://localhost:8080/api/ai/optimize-prompt \
  -H "Content-Type: application/json" \
  -d '{"input": "一只猫在花园里"}'
```

## 🎯 启动成功标志

启动成功后，你会看到：

```
✅ SuperBlog 启动成功！
🌐 访问地址: http://localhost:8080
❤️ 健康检查: http://localhost:8080/api/health
🤖 AI接口测试: POST http://localhost:8080/api/ai/text-to-image
💬 通义千问测试: POST http://localhost:8080/api/ai/chat
```

## 🔍 故障排除

### 问题1：端口被占用
```bash
# 检查端口占用
netstat -ano | findstr :8080

# 或者修改端口
# 在代码中将 int port = 8080; 改为其他端口
```

### 问题2：SLF4J警告
这是正常的，不影响功能。如果想消除警告，确保logback依赖正确。

### 问题3：启动失败
1. 检查Java版本是否为21+
2. 检查Maven依赖是否下载完成
3. 查看控制台错误信息

## 📊 API响应格式

所有API都返回统一的JSON格式：

```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": 1703123456789
}
```

## 🔑 通义千问配置

API密钥已配置：`sk-4606dfde828a4f9aa7a43f5d53dddb9e`

模型：`qwen-turbo`

API文档：https://help.aliyun.com/document_detail/2712576.html

## 🎉 下一步

1. 测试所有API接口
2. 集成真实的通义千问API
3. 添加数据库功能
4. 开发前端界面

现在你可以开始使用SuperBlog的AI功能了！
