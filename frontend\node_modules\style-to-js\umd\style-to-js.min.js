!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).StyleToJS=t()}(this,(function(){"use strict";function e(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var t,r,n,o={};function u(){if(n)return o;n=1;var e=o&&o.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(o,"__esModule",{value:!0}),o.default=function(e,t){var r=null;if(!e||"string"!=typeof e)return r;var n=(0,u.default)(e),o="function"==typeof t;return n.forEach((function(e){if("declaration"===e.type){var n=e.property,u=e.value;o?t(n,u,e):u&&((r=r||{})[n]=u)}})),r};var u=e(function(){if(r)return t;r=1;var e=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,n=/\n/g,o=/^\s*/,u=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,i=/^:\s*/,a=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,f=/^[;\s]*/,c=/^\s+|\s+$/g,l="";function s(e){return e?e.replace(c,l):l}return t=function(t,r){if("string"!=typeof t)throw new TypeError("First argument must be a string");if(!t)return[];r=r||{};var c=1,p=1;function d(e){var t=e.match(n);t&&(c+=t.length);var r=e.lastIndexOf("\n");p=~r?e.length-r:p+e.length}function v(){var e={line:c,column:p};return function(t){return t.position=new m(e),g(),t}}function m(e){this.start=e,this.end={line:c,column:p},this.source=r.source}function h(e){var n=new Error(r.source+":"+c+":"+p+": "+e);if(n.reason=e,n.filename=r.source,n.line=c,n.column=p,n.source=t,!r.silent)throw n}function y(e){var r=e.exec(t);if(r){var n=r[0];return d(n),t=t.slice(n.length),r}}function g(){y(o)}function _(e){var t;for(e=e||[];t=w();)!1!==t&&e.push(t);return e}function w(){var e=v();if("/"==t.charAt(0)&&"*"==t.charAt(1)){for(var r=2;l!=t.charAt(r)&&("*"!=t.charAt(r)||"/"!=t.charAt(r+1));)++r;if(r+=2,l===t.charAt(r-1))return h("End of comment missing");var n=t.slice(2,r-2);return p+=2,d(n),t=t.slice(r),p+=2,e({type:"comment",comment:n})}}function b(){var t=v(),r=y(u);if(r){if(w(),!y(i))return h("property missing ':'");var n=y(a),o=t({type:"declaration",property:s(r[0].replace(e,l)),value:n?s(n[0].replace(e,l)):l});return y(f),o}}return m.prototype.content=t,g(),function(){var e,t=[];for(_(t);e=b();)!1!==e&&(t.push(e),_(t));return t}()}}());return o}var i,a,f,c={};function l(){if(i)return c;i=1,Object.defineProperty(c,"__esModule",{value:!0}),c.camelCase=void 0;var e=/^--[a-zA-Z0-9_-]+$/,t=/-([a-z])/g,r=/^[^-]+$/,n=/^-(webkit|moz|ms|o|khtml)-/,o=/^-(ms)-/,u=function(e,t){return t.toUpperCase()},a=function(e,t){return"".concat(t,"-")};return c.camelCase=function(i,f){return void 0===f&&(f={}),function(t){return!t||r.test(t)||e.test(t)}(i)?i:(i=i.toLowerCase(),(i=f.reactCompat?i.replace(o,a):i.replace(n,a)).replace(t,u))},c}return e(function(){if(f)return a;f=1;var e=(a&&a.__importDefault||function(e){return e&&e.__esModule?e:{default:e}})(u()),t=l();function r(r,n){var o={};return r&&"string"==typeof r?((0,e.default)(r,(function(e,r){e&&r&&(o[(0,t.camelCase)(e,n)]=r)})),o):o}return r.default=r,a=r}())}));
//# sourceMappingURL=style-to-js.min.js.map
